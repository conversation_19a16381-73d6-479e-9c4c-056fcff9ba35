# 图表组件使用文档

## 概述

图表组件为编辑器提供了丰富的数据可视化功能，支持7种基础图表类型，采用 ECharts 按需加载，确保性能优化。

## 功能特性

### 支持的图表类型
- 柱状图 (bar)
- 条形图 (horizontalBar)
- 折线图 (line)
- 面积图 (area)
- 饼图 (pie)
- 环图 (doughnut)
- 散点图 (scatter)
- 雷达图 (radar)

### 核心功能
- ✅ 纯 JSON 数据存储 (schema attributes)
- ✅ 弹窗式图表编辑器
- ✅ 基础模式和高级模式数据编辑
- ✅ 完整的样式配置面板
- ✅ 工具栏和斜杠菜单集成
- ✅ 中英文国际化支持
- ✅ ECharts 按需引入优化
- ✅ 协同编辑兼容

## 数据存储规范

图表数据采用纯 JSON 格式存储在节点的 `data` 属性中：

```json
{
  "type": "bar",
  "title": "销售数据",
  "data": {
    "categories": ["Q1", "Q2", "Q3", "Q4"],
    "series": [
      {
        "name": "销售额",
        "data": [120, 132, 101, 134]
      },
      {
        "name": "利润",
        "data": [20, 30, 25, 40]
      }
    ]
  },
  "config": {
    "theme": "default",
    "showLegend": true,
    "legendPosition": "bottom",
    "legendVerticalPosition": "center",
    "showGrid": true,
    "showTooltip": true
  }
}
```

### 数据结构说明

#### 基础字段
- `type`: 图表类型
- `title`: 图表标题
- `data`: 图表数据对象
- `config`: 样式配置对象

#### 数据对象 (data)
- `categories`: 分类轴数据数组
- `series`: 数据系列数组
  - `name`: 系列名称
  - `data`: 数值数据数组

#### 配置对象 (config)
- `theme`: 主题色 (默认: "default")
- `showLegend`: 显示图例 (默认: true)
- `legendPosition`: 图例位置 ("top"|"bottom"|"left"|"right")
- `legendVerticalPosition`: 图例纵向位置 ("top"|"center"|"bottom")
- `showGrid`: 显示网格 (默认: true)
- `showTooltip`: 显示提示框 (默认: true)

## 使用方式

### 1. 通过工具栏
点击工具栏中的图表按钮 📊，打开图表编辑器。

### 2. 通过斜杠菜单
输入 `/chart` 或 `/图表`，选择图表选项。

### 3. 快捷键
使用快捷键 `Cmd/Ctrl + Shift + C`。

### 4. 编程方式
```javascript
editor.chain().focus().insertContent({
  type: "chart",
  attrs: {
    data: JSON.stringify(chartData)
  }
}).run();
```

## 编辑流程

### 基础模式编辑
1. 选择图表类型
2. 设置图表标题
3. 管理分类数据
4. 管理数据系列
5. 配置样式选项

### 高级模式编辑
直接编辑 ECharts 格式的 JSON 数据，支持更复杂的图表配置。

## 协同编辑支持

图表组件采用编辑独占方式：
- 编辑过程中禁止其他用户同时编辑
- 保存完成后才允许其他用户编辑
- 避免数据冲突和版本问题

## 导出支持

### 在线预览
图表在编辑器中实时渲染，支持响应式布局。

### 静态 HTML 导出
导出时图表会被渲染为静态图片，保持可视效果。

### Word 文档导出
图表被转换为静态图片插入到 Word 文档中。

## 性能优化

### 按需加载
- ECharts 核心库按需导入
- 只加载使用的图表类型组件
- 延迟加载减少初始包体积

### 内存管理
- 图表实例自动清理
- ResizeObserver 正确释放
- 避免内存泄漏

## 样式定制

图表组件使用项目统一的 SCSS 变量系统，支持主题定制：

```scss
// 自定义图表容器样式
.#{$prefix-class}__chart-container {
  // 你的样式
}

// 自定义编辑按钮样式
.#{$prefix-class}__chart-edit-btn {
  // 你的样式
}
```

## 版本兼容性

- ✅ 兼容 TipTap v2 版本
- ✅ 避免使用 TipTap v3 版本依赖
- ✅ 支持现有编辑器功能
- ✅ 向后兼容

## 故障排除

### 常见问题

1. **图表不显示**
   - 检查 ECharts 依赖是否正确安装
   - 确认数据格式是否正确

2. **编辑器打开失败**
   - 检查 `window.openChartEditor` 是否正确注册
   - 确认组件正确导入

3. **样式问题**
   - 确认 chart.scss 已正确导入
   - 检查样式变量是否定义

4. **性能问题**
   - 检查是否正确使用按需加载
   - 确认图表实例是否正确清理

### 调试模式

开启浏览器开发者工具，查看控制台输出：
- 数据解析错误会显示具体信息
- ECharts 加载错误会有详细日志
- 组件渲染过程可以追踪