import{r as B,C as Se,i as Ee,c as h,Q as Zr,b as zo,d as R,e as P,f as Y,s as U,n as N,g as ke,h as ne,q as ie,k as re,t as be,Z as gn,x as _e,w as We,j as T,m as Me,z as Qe,y as Ke,B as dt,F as Fn,D as Yr,l as oe,p as bn,o as Ie,u as Pn,E as ft,$ as No,a0 as Do,G as Wo,T as yn,H as Ro,I as Ho,a as Go,A as et,a1 as Zo,S as Yo}from"./index-BrKIBfdb.js";const Re=Object.prototype.toString;function He(e){return Re.call(e)==="[object Array]"}function Xo(e){return Re.call(e)==="[object Null]"}function qo(e){return Re.call(e)==="[object Boolean]"}function Be(e){return Re.call(e)==="[object Object]"}const nd=e=>Re.call(e)==="[object Promise]";function ut(e){return Re.call(e)==="[object String]"}function de(e){return Re.call(e)==="[object Number]"&&e===e}function lt(e){return e===void 0}function tt(e){return typeof e=="function"}const rd=e=>(e==null?void 0:e.$)!==void 0,Mt=Symbol("ArcoConfigProvider"),yt={formatYear:"YYYY 年",formatMonth:"YYYY 年 MM 月",today:"今天",view:{month:"月",year:"年",week:"周",day:"日"},month:{long:{January:"一月",February:"二月",March:"三月",April:"四月",May:"五月",June:"六月",July:"七月",August:"八月",September:"九月",October:"十月",November:"十一月",December:"十二月"},short:{January:"一月",February:"二月",March:"三月",April:"四月",May:"五月",June:"六月",July:"七月",August:"八月",September:"九月",October:"十月",November:"十一月",December:"十二月"}},week:{long:{self:"周",monday:"周一",tuesday:"周二",wednesday:"周三",thursday:"周四",friday:"周五",saturday:"周六",sunday:"周日"},short:{self:"周",monday:"一",tuesday:"二",wednesday:"三",thursday:"四",friday:"五",saturday:"六",sunday:"日"}}},Jo={locale:"zh-CN",empty:{description:"暂无数据"},drawer:{okText:"确定",cancelText:"取消"},popconfirm:{okText:"确定",cancelText:"取消"},modal:{okText:"确定",cancelText:"取消"},pagination:{goto:"前往",page:"页",countPerPage:"条/页",total:"共 {0} 条"},table:{okText:"确定",resetText:"重置"},upload:{start:"开始",cancel:"取消",delete:"删除",retry:"点击重试",buttonText:"点击上传",preview:"预览",drag:"点击或拖拽文件到此处上传",dragHover:"释放文件并开始上传",error:"上传失败"},calendar:yt,datePicker:{view:yt.view,month:yt.month,week:yt.week,placeholder:{date:"请选择日期",week:"请选择周",month:"请选择月份",year:"请选择年份",quarter:"请选择季度",time:"请选择时间"},rangePlaceholder:{date:["开始日期","结束日期"],week:["开始周","结束周"],month:["开始月份","结束月份"],year:["开始年份","结束年份"],quarter:["开始季度","结束季度"],time:["开始时间","结束时间"]},selectTime:"选择时间",today:"今天",now:"此刻",ok:"确定"},image:{loading:"加载中"},imagePreview:{fullScreen:"全屏",rotateRight:"向右旋转",rotateLeft:"向左旋转",zoomIn:"放大",zoomOut:"缩小",originalSize:"原始尺寸"},typography:{copied:"已复制",copy:"复制",expand:"展开",collapse:"折叠",edit:"编辑"},form:{validateMessages:{required:"#{field} 是必填项",type:{string:"#{field} 不是合法的文本类型",number:"#{field} 不是合法的数字类型",boolean:"#{field} 不是合法的布尔类型",array:"#{field} 不是合法的数组类型",object:"#{field} 不是合法的对象类型",url:"#{field} 不是合法的 url 地址",email:"#{field} 不是合法的邮箱地址",ip:"#{field} 不是合法的 IP 地址"},number:{min:"`#{value}` 小于最小值 `#{min}`",max:"`#{value}` 大于最大值 `#{max}`",equal:"`#{value}` 不等于 `#{equal}`",range:"`#{value}` 不在 `#{min} ~ #{max}` 范围内",positive:"`#{value}` 不是正数",negative:"`#{value}` 不是负数"},array:{length:"`#{field}` 个数不等于 #{length}",minLength:"`#{field}` 个数最少为 #{minLength}",maxLength:"`#{field}` 个数最多为 #{maxLength}",includes:"#{field} 不包含 #{includes}",deepEqual:"#{field} 不等于 #{deepEqual}",empty:"`#{field}` 不是空数组"},string:{minLength:"字符数最少为 #{minLength}",maxLength:"字符数最多为 #{maxLength}",length:"字符数必须是 #{length}",match:"`#{value}` 不符合模式 #{pattern}",uppercase:"`#{value}` 必须全大写",lowercase:"`#{value}` 必须全小写"},object:{deepEqual:"`#{field}` 不等于期望值",hasKeys:"`#{field}` 不包含必须字段",empty:"`#{field}` 不是对象"},boolean:{true:"期望是 `true`",false:"期望是 `false`"}}},colorPicker:{history:"最近使用颜色",preset:"系统预设颜色",empty:"暂无"}},Uo=B("zh-CN"),Ko=Se({"zh-CN":Jo}),Qo=()=>{const e=Ee(Mt,void 0),t=h(()=>{var o;return(o=e==null?void 0:e.locale)!=null?o:Ko[Uo.value]}),n=h(()=>t.value.locale);return{i18nMessage:t,locale:n,t:(o,...i)=>{const a=o.split(".");let l=t.value;for(const s of a){if(!l[s])return o;l=l[s]}return ut(l)&&i.length>0?l.replace(/{(\d+)}/g,(s,u)=>{var d;return(d=i[u])!=null?d:s}):l}}};var ei=Object.defineProperty,ti=Object.defineProperties,ni=Object.getOwnPropertyDescriptors,Gn=Object.getOwnPropertySymbols,ri=Object.prototype.hasOwnProperty,oi=Object.prototype.propertyIsEnumerable,Zn=(e,t,n)=>t in e?ei(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ii=(e,t)=>{for(var n in t||(t={}))ri.call(t,n)&&Zn(e,n,t[n]);if(Gn)for(var n of Gn(t))oi.call(t,n)&&Zn(e,n,t[n]);return e},ai=(e,t)=>ti(e,ni(t));const li="A",si="arco",wn="$arco",vt=e=>{var t;return(t=e==null?void 0:e.componentPrefix)!=null?t:li},ht=(e,t)=>{var n;t&&t.classPrefix&&(e.config.globalProperties[wn]=ai(ii({},(n=e.config.globalProperties[wn])!=null?n:{}),{classPrefix:t.classPrefix}))},X=e=>{var t,n,r;const o=Zr(),i=Ee(Mt,void 0),a=(r=(n=i==null?void 0:i.prefixCls)!=null?n:(t=o==null?void 0:o.appContext.config.globalProperties[wn])==null?void 0:t.classPrefix)!=null?r:si;return e?`${a}-${e}`:a};var Xr=function(){if(typeof Map<"u")return Map;function e(t,n){var r=-1;return t.some(function(o,i){return o[0]===n?(r=i,!0):!1}),r}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(n){var r=e(this.__entries__,n),o=this.__entries__[r];return o&&o[1]},t.prototype.set=function(n,r){var o=e(this.__entries__,n);~o?this.__entries__[o][1]=r:this.__entries__.push([n,r])},t.prototype.delete=function(n){var r=this.__entries__,o=e(r,n);~o&&r.splice(o,1)},t.prototype.has=function(n){return!!~e(this.__entries__,n)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(n,r){r===void 0&&(r=null);for(var o=0,i=this.__entries__;o<i.length;o++){var a=i[o];n.call(r,a[1],a[0])}},t}()}(),Cn=typeof window<"u"&&typeof document<"u"&&window.document===document,Ot=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),ui=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(Ot):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)}}(),ci=2;function di(e,t){var n=!1,r=!1,o=0;function i(){n&&(n=!1,e()),r&&l()}function a(){ui(i)}function l(){var s=Date.now();if(n){if(s-o<ci)return;r=!0}else n=!0,r=!1,setTimeout(a,t);o=s}return l}var fi=20,vi=["top","right","bottom","left","width","height","size","weight"],hi=typeof MutationObserver<"u",pi=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=di(this.refresh.bind(this),fi)}return e.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},e.prototype.removeObserver=function(t){var n=this.observers_,r=n.indexOf(t);~r&&n.splice(r,1),!n.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var t=this.updateObservers_();t&&this.refresh()},e.prototype.updateObservers_=function(){var t=this.observers_.filter(function(n){return n.gatherActive(),n.hasActive()});return t.forEach(function(n){return n.broadcastActive()}),t.length>0},e.prototype.connect_=function(){!Cn||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),hi?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){!Cn||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(t){var n=t.propertyName,r=n===void 0?"":n,o=vi.some(function(i){return!!~r.indexOf(i)});o&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),qr=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var o=r[n];Object.defineProperty(e,o,{value:t[o],enumerable:!1,writable:!1,configurable:!0})}return e},nt=function(e){var t=e&&e.ownerDocument&&e.ownerDocument.defaultView;return t||Ot},Jr=Vt(0,0,0,0);function Lt(e){return parseFloat(e)||0}function Yn(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(r,o){var i=e["border-"+o+"-width"];return r+Lt(i)},0)}function mi(e){for(var t=["top","right","bottom","left"],n={},r=0,o=t;r<o.length;r++){var i=o[r],a=e["padding-"+i];n[i]=Lt(a)}return n}function gi(e){var t=e.getBBox();return Vt(0,0,t.width,t.height)}function bi(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return Jr;var r=nt(e).getComputedStyle(e),o=mi(r),i=o.left+o.right,a=o.top+o.bottom,l=Lt(r.width),s=Lt(r.height);if(r.boxSizing==="border-box"&&(Math.round(l+i)!==t&&(l-=Yn(r,"left","right")+i),Math.round(s+a)!==n&&(s-=Yn(r,"top","bottom")+a)),!wi(e)){var u=Math.round(l+i)-t,d=Math.round(s+a)-n;Math.abs(u)!==1&&(l-=u),Math.abs(d)!==1&&(s-=d)}return Vt(o.left,o.top,l,s)}var yi=function(){return typeof SVGGraphicsElement<"u"?function(e){return e instanceof nt(e).SVGGraphicsElement}:function(e){return e instanceof nt(e).SVGElement&&typeof e.getBBox=="function"}}();function wi(e){return e===nt(e).document.documentElement}function Ci(e){return Cn?yi(e)?gi(e):bi(e):Jr}function Si(e){var t=e.x,n=e.y,r=e.width,o=e.height,i=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,a=Object.create(i.prototype);return qr(a,{x:t,y:n,width:r,height:o,top:n,right:t+r,bottom:o+n,left:t}),a}function Vt(e,t,n,r){return{x:e,y:t,width:n,height:r}}var Ei=function(){function e(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Vt(0,0,0,0),this.target=t}return e.prototype.isActive=function(){var t=Ci(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},e}(),_i=function(){function e(t,n){var r=Si(n);qr(this,{target:t,contentRect:r})}return e}(),ki=function(){function e(t,n,r){if(this.activeObservations_=[],this.observations_=new Xr,typeof t!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=n,this.callbackCtx_=r}return e.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof nt(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(t)||(n.set(t,new Ei(t)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(t instanceof nt(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(t)&&(n.delete(t),n.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(n){n.isActive()&&t.activeObservations_.push(n)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,n=this.activeObservations_.map(function(r){return new _i(r.target,r.broadcastRect())});this.callback_.call(t,n,t),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),Ur=typeof WeakMap<"u"?new WeakMap:new Xr,Kr=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=pi.getInstance(),r=new ki(t,n,this);Ur.set(this,r)}return e}();["observe","unobserve","disconnect"].forEach(function(e){Kr.prototype[e]=function(){var t;return(t=Ur.get(this))[e].apply(t,arguments)}});var Qr=function(){return typeof Ot.ResizeObserver<"u"?Ot.ResizeObserver:Kr}(),Xn;(function(e){e[e.ELEMENT=1]="ELEMENT",e[e.FUNCTIONAL_COMPONENT=2]="FUNCTIONAL_COMPONENT",e[e.STATEFUL_COMPONENT=4]="STATEFUL_COMPONENT",e[e.COMPONENT=6]="COMPONENT",e[e.TEXT_CHILDREN=8]="TEXT_CHILDREN",e[e.ARRAY_CHILDREN=16]="ARRAY_CHILDREN",e[e.SLOTS_CHILDREN=32]="SLOTS_CHILDREN",e[e.TELEPORT=64]="TELEPORT",e[e.SUSPENSE=128]="SUSPENSE",e[e.COMPONENT_SHOULD_KEEP_ALIVE=256]="COMPONENT_SHOULD_KEEP_ALIVE",e[e.COMPONENT_KEPT_ALIVE=512]="COMPONENT_KEPT_ALIVE"})(Xn||(Xn={}));var qn;(function(e){e[e.TEXT=1]="TEXT",e[e.CLASS=2]="CLASS",e[e.STYLE=4]="STYLE",e[e.PROPS=8]="PROPS",e[e.FULL_PROPS=16]="FULL_PROPS",e[e.HYDRATE_EVENTS=32]="HYDRATE_EVENTS",e[e.STABLE_FRAGMENT=64]="STABLE_FRAGMENT",e[e.KEYED_FRAGMENT=128]="KEYED_FRAGMENT",e[e.UNKEYED_FRAGMENT=256]="UNKEYED_FRAGMENT",e[e.NEED_PATCH=512]="NEED_PATCH",e[e.DYNAMIC_SLOTS=1024]="DYNAMIC_SLOTS",e[e.DEV_ROOT_FRAGMENT=2048]="DEV_ROOT_FRAGMENT",e[e.HOISTED=-1]="HOISTED",e[e.BAIL=-2]="BAIL"})(qn||(qn={}));const At=e=>!!(e&&e.shapeFlag&1),Tt=(e,t)=>!!(e&&e.shapeFlag&6),$i=(e,t)=>!!(e&&e.shapeFlag&8),xn=(e,t)=>!!(e&&e.shapeFlag&16),eo=(e,t)=>!!(e&&e.shapeFlag&32),Rt=e=>{var t,n;if(e)for(const r of e){if(At(r)||Tt(r))return r;if(xn(r,r.children)){const o=Rt(r.children);if(o)return o}else if(eo(r,r.children)){const o=(n=(t=r.children).default)==null?void 0:n.call(t);if(o){const i=Rt(o);if(i)return i}}else if(He(r)){const o=Rt(r);if(o)return o}}},Oi=e=>{if(!e)return!0;for(const t of e)if(t.children)return!1;return!0},to=(e,t)=>{if(e&&e.length>0)for(let n=0;n<e.length;n++){const r=e[n];if(At(r)||Tt(r)){const i=tt(t)?t(r):t;return e[n]=zo(r,i,!0),!0}const o=no(r);if(o&&o.length>0&&to(o,t))return!0}return!1},no=e=>{if(xn(e,e.children))return e.children;if(He(e))return e},ro=e=>{var t,n;if(At(e))return e.el;if(Tt(e)){if(((t=e.el)==null?void 0:t.nodeType)===1)return e.el;if((n=e.component)!=null&&n.subTree){const r=ro(e.component.subTree);if(r)return r}}else{const r=no(e);return oo(r)}},oo=e=>{if(e&&e.length>0)for(const t of e){const n=ro(t);if(n)return n}},Ht=(e,t=!1)=>{var n,r;const o=[];for(const i of e??[])At(i)||Tt(i)||t&&$i(i,i.children)?o.push(i):xn(i,i.children)?o.push(...Ht(i.children,t)):eo(i,i.children)?o.push(...Ht((r=(n=i.children).default)==null?void 0:r.call(n),t)):He(i)&&o.push(...Ht(i,t));return o},od=e=>{if(e)return tt(e)?e:()=>e},io=typeof window>"u"?global:window,Li=io.requestAnimationFrame,Jn=io.cancelAnimationFrame;function Un(e){let t=0;const n=(...r)=>{t&&Jn(t),t=Li(()=>{e(...r),t=0})};return n.cancel=()=>{Jn(t),t=0},n}const Mn=()=>{},ao=()=>{const{body:e}=document,t=document.documentElement;let n;try{n=(window.top||window.self||window).document.body}catch{}return{height:Math.max(e.scrollHeight,e.offsetHeight,t.clientHeight,t.scrollHeight,t.offsetHeight,(n==null?void 0:n.scrollHeight)||0,(n==null?void 0:n.clientHeight)||0),width:Math.max(e.scrollWidth,e.offsetWidth,t.clientWidth,t.scrollWidth,t.offsetWidth,(n==null?void 0:n.scrollWidth)||0,(n==null?void 0:n.clientWidth)||0)}},Vn=(()=>{try{return!(typeof window<"u"&&document!==void 0)}catch{return!0}})(),Gt=Vn?Mn:(e,t,n,r=!1)=>{e.addEventListener(t,n,r)},Kn=Vn?Mn:(e,t,n,r=!1)=>{e.removeEventListener(t,n,r)},id=(e,t)=>{if(!e||!t)return!1;let n=t;for(;n;){if(n===e)return!0;n=n.parentNode}return!1},ad=e=>{const t=document.createElement("div");return t.setAttribute("class",`arco-overlay arco-overlay-${e}`),t},ji=(e,t)=>{var n;return Vn?Mn():(n=document.querySelector(e))!=null?n:void 0},Qn=(e,t)=>{if(ut(e)){const n=e[0]==="#"?`[id='${e.slice(1)}']`:e;return ji(n)}return e},ld=e=>e.tagName==="BODY"?document.documentElement.scrollHeight>window.innerHeight:e.scrollHeight>e.offsetHeight,sd=e=>e.tagName==="BODY"?window.innerWidth-ao().width:e.offsetWidth-e.clientWidth;var K=(e,t)=>{for(const[n,r]of t)e[n]=r;return e};const Fi=R({name:"IconHover",props:{prefix:{type:String},size:{type:String,default:"medium"},disabled:{type:Boolean,default:!1}},setup(){return{prefixCls:X("icon-hover")}}});function Pi(e,t,n,r,o,i){return P(),Y("span",{class:N([e.prefixCls,{[`${e.prefix}-icon-hover`]:e.prefix,[`${e.prefixCls}-size-${e.size}`]:e.size!=="medium",[`${e.prefixCls}-disabled`]:e.disabled}])},[U(e.$slots,"default")],2)}var An=K(Fi,[["render",Pi]]);const xi=R({name:"IconClose",props:{size:{type:[Number,String]},strokeWidth:{type:Number,default:4},strokeLinecap:{type:String,default:"butt",validator:e=>["butt","round","square"].includes(e)},strokeLinejoin:{type:String,default:"miter",validator:e=>["arcs","bevel","miter","miter-clip","round"].includes(e)},rotate:Number,spin:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const n=X("icon"),r=h(()=>[n,`${n}-close`,{[`${n}-spin`]:e.spin}]),o=h(()=>{const a={};return e.size&&(a.fontSize=de(e.size)?`${e.size}px`:e.size),e.rotate&&(a.transform=`rotate(${e.rotate}deg)`),a});return{cls:r,innerStyle:o,onClick:a=>{t("click",a)}}}}),Mi=["stroke-width","stroke-linecap","stroke-linejoin"],Vi=ne("path",{d:"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142"},null,-1),Ai=[Vi];function Ti(e,t,n,r,o,i){return P(),Y("svg",{viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor",class:N(e.cls),style:ke(e.innerStyle),"stroke-width":e.strokeWidth,"stroke-linecap":e.strokeLinecap,"stroke-linejoin":e.strokeLinejoin,onClick:t[0]||(t[0]=(...a)=>e.onClick&&e.onClick(...a))},Ai,14,Mi)}var Zt=K(xi,[["render",Ti]]);const Bi=Object.assign(Zt,{install:(e,t)=>{var n;const r=(n=t==null?void 0:t.iconPrefix)!=null?n:"";e.component(r+Zt.name,Zt)}}),Ii=R({name:"IconCheckCircleFill",props:{size:{type:[Number,String]},strokeWidth:{type:Number,default:4},strokeLinecap:{type:String,default:"butt",validator:e=>["butt","round","square"].includes(e)},strokeLinejoin:{type:String,default:"miter",validator:e=>["arcs","bevel","miter","miter-clip","round"].includes(e)},rotate:Number,spin:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const n=X("icon"),r=h(()=>[n,`${n}-check-circle-fill`,{[`${n}-spin`]:e.spin}]),o=h(()=>{const a={};return e.size&&(a.fontSize=de(e.size)?`${e.size}px`:e.size),e.rotate&&(a.transform=`rotate(${e.rotate}deg)`),a});return{cls:r,innerStyle:o,onClick:a=>{t("click",a)}}}}),zi=["stroke-width","stroke-linecap","stroke-linejoin"],Ni=ne("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z",fill:"currentColor",stroke:"none"},null,-1),Di=[Ni];function Wi(e,t,n,r,o,i){return P(),Y("svg",{viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor",class:N(e.cls),style:ke(e.innerStyle),"stroke-width":e.strokeWidth,"stroke-linecap":e.strokeLinecap,"stroke-linejoin":e.strokeLinejoin,onClick:t[0]||(t[0]=(...a)=>e.onClick&&e.onClick(...a))},Di,14,zi)}var Yt=K(Ii,[["render",Wi]]);const Ri=Object.assign(Yt,{install:(e,t)=>{var n;const r=(n=t==null?void 0:t.iconPrefix)!=null?n:"";e.component(r+Yt.name,Yt)}}),Hi=R({name:"IconExclamationCircleFill",props:{size:{type:[Number,String]},strokeWidth:{type:Number,default:4},strokeLinecap:{type:String,default:"butt",validator:e=>["butt","round","square"].includes(e)},strokeLinejoin:{type:String,default:"miter",validator:e=>["arcs","bevel","miter","miter-clip","round"].includes(e)},rotate:Number,spin:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const n=X("icon"),r=h(()=>[n,`${n}-exclamation-circle-fill`,{[`${n}-spin`]:e.spin}]),o=h(()=>{const a={};return e.size&&(a.fontSize=de(e.size)?`${e.size}px`:e.size),e.rotate&&(a.transform=`rotate(${e.rotate}deg)`),a});return{cls:r,innerStyle:o,onClick:a=>{t("click",a)}}}}),Gi=["stroke-width","stroke-linecap","stroke-linejoin"],Zi=ne("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm-2-11a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v2Zm4-18a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V15Z",fill:"currentColor",stroke:"none"},null,-1),Yi=[Zi];function Xi(e,t,n,r,o,i){return P(),Y("svg",{viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor",class:N(e.cls),style:ke(e.innerStyle),"stroke-width":e.strokeWidth,"stroke-linecap":e.strokeLinecap,"stroke-linejoin":e.strokeLinejoin,onClick:t[0]||(t[0]=(...a)=>e.onClick&&e.onClick(...a))},Yi,14,Gi)}var Xt=K(Hi,[["render",Xi]]);const qi=Object.assign(Xt,{install:(e,t)=>{var n;const r=(n=t==null?void 0:t.iconPrefix)!=null?n:"";e.component(r+Xt.name,Xt)}}),Ji=R({name:"IconCloseCircleFill",props:{size:{type:[Number,String]},strokeWidth:{type:Number,default:4},strokeLinecap:{type:String,default:"butt",validator:e=>["butt","round","square"].includes(e)},strokeLinejoin:{type:String,default:"miter",validator:e=>["arcs","bevel","miter","miter-clip","round"].includes(e)},rotate:Number,spin:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const n=X("icon"),r=h(()=>[n,`${n}-close-circle-fill`,{[`${n}-spin`]:e.spin}]),o=h(()=>{const a={};return e.size&&(a.fontSize=de(e.size)?`${e.size}px`:e.size),e.rotate&&(a.transform=`rotate(${e.rotate}deg)`),a});return{cls:r,innerStyle:o,onClick:a=>{t("click",a)}}}}),Ui=["stroke-width","stroke-linecap","stroke-linejoin"],Ki=ne("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm4.955-27.771-4.95 4.95-4.95-4.95a1 1 0 0 0-1.414 0l-1.414 1.414a1 1 0 0 0 0 1.414l4.95 4.95-4.95 4.95a1 1 0 0 0 0 1.414l1.414 1.414a1 1 0 0 0 1.414 0l4.95-4.95 4.95 4.95a1 1 0 0 0 1.414 0l1.414-1.414a1 1 0 0 0 0-1.414l-4.95-4.95 4.95-4.95a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0Z",fill:"currentColor",stroke:"none"},null,-1),Qi=[Ki];function ea(e,t,n,r,o,i){return P(),Y("svg",{viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor",class:N(e.cls),style:ke(e.innerStyle),"stroke-width":e.strokeWidth,"stroke-linecap":e.strokeLinecap,"stroke-linejoin":e.strokeLinejoin,onClick:t[0]||(t[0]=(...a)=>e.onClick&&e.onClick(...a))},Qi,14,Ui)}var qt=K(Ji,[["render",ea]]);const ta=Object.assign(qt,{install:(e,t)=>{var n;const r=(n=t==null?void 0:t.iconPrefix)!=null?n:"";e.component(r+qt.name,qt)}});function er(e){return typeof e=="object"&&e!=null&&e.nodeType===1}function tr(e,t){return(!t||e!=="hidden")&&e!=="visible"&&e!=="clip"}function Jt(e,t){if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){var n=getComputedStyle(e,null);return tr(n.overflowY,t)||tr(n.overflowX,t)||function(r){var o=function(i){if(!i.ownerDocument||!i.ownerDocument.defaultView)return null;try{return i.ownerDocument.defaultView.frameElement}catch{return null}}(r);return!!o&&(o.clientHeight<r.scrollHeight||o.clientWidth<r.scrollWidth)}(e)}return!1}function wt(e,t,n,r,o,i,a,l){return i<e&&a>t||i>e&&a<t?0:i<=e&&l<=n||a>=t&&l>=n?i-e-r:a>t&&l<n||i<e&&l>n?a-t+o:0}var nr=function(e,t){var n=window,r=t.scrollMode,o=t.block,i=t.inline,a=t.boundary,l=t.skipOverflowHiddenElements,s=typeof a=="function"?a:function(V){return V!==a};if(!er(e))throw new TypeError("Invalid target");for(var u,d,c=document.scrollingElement||document.documentElement,f=[],v=e;er(v)&&s(v);){if((v=(d=(u=v).parentElement)==null?u.getRootNode().host||null:d)===c){f.push(v);break}v!=null&&v===document.body&&Jt(v)&&!Jt(document.documentElement)||v!=null&&Jt(v,l)&&f.push(v)}for(var y=n.visualViewport?n.visualViewport.width:innerWidth,S=n.visualViewport?n.visualViewport.height:innerHeight,_=window.scrollX||pageXOffset,m=window.scrollY||pageYOffset,w=e.getBoundingClientRect(),k=w.height,x=w.width,A=w.top,H=w.right,$=w.bottom,C=w.left,D=o==="start"||o==="nearest"?A:o==="end"?$:A+k/2,z=i==="center"?C+x/2:i==="end"?H:C,te=[],q=0;q<f.length;q++){var O=f[q],se=O.getBoundingClientRect(),ae=se.height,Q=se.width,J=se.top,L=se.right,M=se.bottom,p=se.left;if(r==="if-needed"&&A>=0&&C>=0&&$<=S&&H<=y&&A>=J&&$<=M&&C>=p&&H<=L)return te;var E=getComputedStyle(O),F=parseInt(E.borderLeftWidth,10),W=parseInt(E.borderTopWidth,10),le=parseInt(E.borderRightWidth,10),ve=parseInt(E.borderBottomWidth,10),G=0,pe=0,he="offsetWidth"in O?O.offsetWidth-O.clientWidth-F-le:0,we="offsetHeight"in O?O.offsetHeight-O.clientHeight-W-ve:0,Ge="offsetWidth"in O?O.offsetWidth===0?0:Q/O.offsetWidth:0,Pe="offsetHeight"in O?O.offsetHeight===0?0:ae/O.offsetHeight:0;if(c===O)G=o==="start"?D:o==="end"?D-S:o==="nearest"?wt(m,m+S,S,W,ve,m+D,m+D+k,k):D-S/2,pe=i==="start"?z:i==="center"?z-y/2:i==="end"?z-y:wt(_,_+y,y,F,le,_+z,_+z+x,x),G=Math.max(0,G+m),pe=Math.max(0,pe+_);else{G=o==="start"?D-J-W:o==="end"?D-M+ve+we:o==="nearest"?wt(J,M,ae,W,ve+we,D,D+k,k):D-(J+ae/2)+we/2,pe=i==="start"?z-p-F:i==="center"?z-(p+Q/2)+he/2:i==="end"?z-L+le+he:wt(p,L,Q,F,le+he,z,z+x,x);var Ze=O.scrollLeft,b=O.scrollTop;D+=b-(G=Math.max(0,Math.min(b+G/Pe,O.scrollHeight-ae/Pe+we))),z+=Ze-(pe=Math.max(0,Math.min(Ze+pe/Ge,O.scrollWidth-Q/Ge+he)))}te.push({el:O,top:G,left:pe})}return te};const ud=["info","success","warning","error"],rr=["onFocus","onFocusin","onFocusout","onBlur","onChange","onBeforeinput","onInput","onReset","onSubmit","onInvalid","onKeydown","onKeypress","onKeyup","onCopy","onCut","onPaste","onCompositionstart","onCompositionupdate","onCompositionend","onSelect","autocomplete","autofocus","maxlength","minlength","name","pattern","readonly","required"],na=R({name:"IconLoading",props:{size:{type:[Number,String]},strokeWidth:{type:Number,default:4},strokeLinecap:{type:String,default:"butt",validator:e=>["butt","round","square"].includes(e)},strokeLinejoin:{type:String,default:"miter",validator:e=>["arcs","bevel","miter","miter-clip","round"].includes(e)},rotate:Number,spin:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const n=X("icon"),r=h(()=>[n,`${n}-loading`,{[`${n}-spin`]:e.spin}]),o=h(()=>{const a={};return e.size&&(a.fontSize=de(e.size)?`${e.size}px`:e.size),e.rotate&&(a.transform=`rotate(${e.rotate}deg)`),a});return{cls:r,innerStyle:o,onClick:a=>{t("click",a)}}}}),ra=["stroke-width","stroke-linecap","stroke-linejoin"],oa=ne("path",{d:"M42 24c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6"},null,-1),ia=[oa];function aa(e,t,n,r,o,i){return P(),Y("svg",{viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor",class:N(e.cls),style:ke(e.innerStyle),"stroke-width":e.strokeWidth,"stroke-linecap":e.strokeLinecap,"stroke-linejoin":e.strokeLinejoin,onClick:t[0]||(t[0]=(...a)=>e.onClick&&e.onClick(...a))},ia,14,ra)}var Ut=K(na,[["render",aa]]);const Tn=Object.assign(Ut,{install:(e,t)=>{var n;const r=(n=t==null?void 0:t.iconPrefix)!=null?n:"";e.component(r+Ut.name,Ut)}}),la=R({name:"FeedbackIcon",components:{IconLoading:Tn,IconCheckCircleFill:Ri,IconExclamationCircleFill:qi,IconCloseCircleFill:ta},props:{type:{type:String}},setup(e){const t=X("feedback-icon");return{cls:h(()=>[t,`${t}-status-${e.type}`])}}});function sa(e,t,n,r,o,i){const a=ie("icon-loading"),l=ie("icon-check-circle-fill"),s=ie("icon-exclamation-circle-fill"),u=ie("icon-close-circle-fill");return P(),Y("span",{class:N(e.cls)},[e.type==="validating"?(P(),re(a,{key:0})):e.type==="success"?(P(),re(l,{key:1})):e.type==="warning"?(P(),re(s,{key:2})):e.type==="error"?(P(),re(u,{key:3})):be("v-if",!0)],2)}var ua=K(la,[["render",sa]]);const ca={key:"Enter",code:"Enter"};var da=Object.defineProperty,or=Object.getOwnPropertySymbols,fa=Object.prototype.hasOwnProperty,va=Object.prototype.propertyIsEnumerable,ir=(e,t,n)=>t in e?da(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ha=(e,t)=>{for(var n in t||(t={}))fa.call(t,n)&&ir(e,n,t[n]);if(or)for(var n of or(t))va.call(t,n)&&ir(e,n,t[n]);return e};const lo=(e,t)=>{const n=ha({},e);for(const r of t)r in n&&delete n[r];return n};function so(e,t){const n={};return t.forEach(r=>{const o=r;r in e&&(n[o]=e[o])}),n}const Sn=Symbol("ArcoFormItemContext"),Bn=Symbol("ArcoFormContext"),uo=({size:e,disabled:t,error:n,uninject:r}={})=>{const o=r?{}:Ee(Sn,{}),i=h(()=>{var d;return(d=e==null?void 0:e.value)!=null?d:o.size}),a=h(()=>(t==null?void 0:t.value)||o.disabled),l=h(()=>(n==null?void 0:n.value)||o.error),s=gn(o,"feedback"),u=gn(o,"eventHandlers");return{formItemCtx:o,mergedSize:i,mergedDisabled:a,mergedError:l,feedback:s,eventHandlers:u}},Bt=(e,{defaultValue:t="medium"}={})=>{const n=Ee(Mt,void 0);return{mergedSize:h(()=>{var o,i;return(i=(o=e==null?void 0:e.value)!=null?o:n==null?void 0:n.size)!=null?i:t})}};function pa(e){const t=B();function n(){if(!e.value)return;const{selectionStart:o,selectionEnd:i,value:a}=e.value;if(o==null||i==null)return;const l=a.slice(0,Math.max(0,o)),s=a.slice(Math.max(0,i));t.value={selectionStart:o,selectionEnd:i,value:a,beforeTxt:l,afterTxt:s}}function r(){if(!e.value||!t.value)return;const{value:o}=e.value,{beforeTxt:i,afterTxt:a,selectionStart:l}=t.value;if(!i||!a||!l)return;let s=o.length;if(o.endsWith(a))s=o.length-a.length;else if(o.startsWith(i))s=i.length;else{const u=i[l-1],d=o.indexOf(u,l-1);d!==-1&&(s=d+1)}e.value.setSelectionRange(s,s)}return[n,r]}var ma=Object.defineProperty,ar=Object.getOwnPropertySymbols,ga=Object.prototype.hasOwnProperty,ba=Object.prototype.propertyIsEnumerable,lr=(e,t,n)=>t in e?ma(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,sr=(e,t)=>{for(var n in t||(t={}))ga.call(t,n)&&lr(e,n,t[n]);if(ar)for(var n of ar(t))ba.call(t,n)&&lr(e,n,t[n]);return e},st=R({name:"Input",inheritAttrs:!1,props:{modelValue:String,defaultValue:{type:String,default:""},size:{type:String},allowClear:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},error:{type:Boolean,default:!1},placeholder:String,maxLength:{type:[Number,Object],default:0},showWordLimit:{type:Boolean,default:!1},wordLength:{type:Function},wordSlice:{type:Function},inputAttrs:{type:Object},type:{type:String,default:"text"}},emits:{"update:modelValue":e=>!0,input:(e,t)=>!0,change:(e,t)=>!0,pressEnter:e=>!0,clear:e=>!0,focus:e=>!0,blur:e=>!0},setup(e,{emit:t,slots:n,attrs:r}){const{size:o,disabled:i,error:a,modelValue:l}=_e(e),s=X("input"),u=B(),{mergedSize:d,mergedDisabled:c,mergedError:f,feedback:v,eventHandlers:y}=uo({size:o,disabled:i,error:a}),{mergedSize:S}=Bt(d),[_,m]=pa(u),w=B(e.defaultValue),k=h(()=>{var b;return(b=e.modelValue)!=null?b:w.value});We(l,b=>{(lt(b)||Xo(b))&&(w.value="")});let x=k.value;const A=B(!1),H=h(()=>e.allowClear&&!e.readonly&&!c.value&&!!k.value),$=B(!1),C=B(""),D=b=>{var V;return tt(e.wordLength)?e.wordLength(b):(V=b.length)!=null?V:0},z=h(()=>D(k.value)),te=h(()=>f.value||!!(Be(e.maxLength)&&e.maxLength.errorOnly&&z.value>O.value)),q=h(()=>Be(e.maxLength)&&!!e.maxLength.errorOnly),O=h(()=>Be(e.maxLength)?e.maxLength.length:e.maxLength),se=h(()=>{const b=D("a");return Math.floor(O.value/b)}),ae=b=>{var V,ee;O.value&&!q.value&&D(b)>O.value&&(b=(ee=(V=e.wordSlice)==null?void 0:V.call(e,b,O.value))!=null?ee:b.slice(0,se.value)),w.value=b,t("update:modelValue",b)},Q=b=>{u.value&&b.target!==u.value&&(b.preventDefault(),u.value.focus())},J=(b,V)=>{var ee,me;b!==x&&(x=b,t("change",b,V),(me=(ee=y.value)==null?void 0:ee.onChange)==null||me.call(ee,V))},L=b=>{var V,ee;A.value=!0,x=k.value,t("focus",b),(ee=(V=y.value)==null?void 0:V.onFocus)==null||ee.call(V,b)},M=b=>{var V,ee;A.value=!1,J(k.value,b),t("blur",b),(ee=(V=y.value)==null?void 0:V.onBlur)==null||ee.call(V,b)},p=b=>{var V,ee,me;const{value:ot,selectionStart:Nt,selectionEnd:Dt}=b.target;if(b.type==="compositionend"){if($.value=!1,C.value="",O.value&&!q.value&&z.value>=O.value&&D(ot)>O.value&&Nt===Dt){E();return}ae(ot),t("input",ot,b),(ee=(V=y.value)==null?void 0:V.onInput)==null||ee.call(V,b),E()}else $.value=!0,C.value=k.value+((me=b.data)!=null?me:"")},E=()=>{_(),Ke(()=>{u.value&&k.value!==u.value.value&&(u.value.value=k.value,m())})},F=b=>{var V,ee;const{value:me}=b.target;if(!$.value){if(O.value&&!q.value&&z.value>=O.value&&D(me)>O.value&&b.inputType==="insertText"){E();return}ae(me),t("input",me,b),(ee=(V=y.value)==null?void 0:V.onInput)==null||ee.call(V,b),E()}},W=b=>{ae(""),J("",b),t("clear",b)},le=b=>{const V=b.key||b.code;!$.value&&V===ca.key&&(J(k.value,b),t("pressEnter",b))},ve=h(()=>[`${s}-outer`,`${s}-outer-size-${S.value}`,{[`${s}-outer-has-suffix`]:!!n.suffix,[`${s}-outer-disabled`]:c.value}]),G=h(()=>[`${s}-wrapper`,{[`${s}-error`]:te.value,[`${s}-disabled`]:c.value,[`${s}-focus`]:A.value}]),pe=h(()=>[s,`${s}-size-${S.value}`]),he=h(()=>lo(r,rr)),we=h(()=>so(r,rr)),Ge=h(()=>{const b=sr(sr({},we.value),e.inputAttrs);return te.value&&(b["aria-invalid"]=!0),b}),Pe=b=>{var V;return T("span",Me({class:G.value,onMousedown:Q},b?void 0:he.value),[n.prefix&&T("span",{class:`${s}-prefix`},[n.prefix()]),T("input",Me({ref:u,class:pe.value,value:k.value,type:e.type,placeholder:e.placeholder,readonly:e.readonly,disabled:c.value,onInput:F,onKeydown:le,onFocus:L,onBlur:M,onCompositionstart:p,onCompositionupdate:p,onCompositionend:p},Ge.value),null),H.value&&T(An,{prefix:s,class:`${s}-clear-btn`,onClick:W},{default:()=>[T(Bi,null,null)]}),(n.suffix||!!e.maxLength&&e.showWordLimit||!!v.value)&&T("span",{class:[`${s}-suffix`,{[`${s}-suffix-has-feedback`]:v.value}]},[!!e.maxLength&&e.showWordLimit&&T("span",{class:`${s}-word-limit`},[z.value,Qe("/"),O.value]),(V=n.suffix)==null?void 0:V.call(n),!!v.value&&T(ua,{type:v.value},null)])])};return{inputRef:u,render:()=>n.prepend||n.append?T("span",Me({class:ve.value},he.value),[n.prepend&&T("span",{class:`${s}-prepend`},[n.prepend()]),Pe(!0),n.append&&T("span",{class:`${s}-append`},[n.append()])]):Pe()}},methods:{focus(){var e;(e=this.inputRef)==null||e.focus()},blur(){var e;(e=this.inputRef)==null||e.blur()}},render(){return this.render()}});const ya=R({name:"IconSearch",props:{size:{type:[Number,String]},strokeWidth:{type:Number,default:4},strokeLinecap:{type:String,default:"butt",validator:e=>["butt","round","square"].includes(e)},strokeLinejoin:{type:String,default:"miter",validator:e=>["arcs","bevel","miter","miter-clip","round"].includes(e)},rotate:Number,spin:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const n=X("icon"),r=h(()=>[n,`${n}-search`,{[`${n}-spin`]:e.spin}]),o=h(()=>{const a={};return e.size&&(a.fontSize=de(e.size)?`${e.size}px`:e.size),e.rotate&&(a.transform=`rotate(${e.rotate}deg)`),a});return{cls:r,innerStyle:o,onClick:a=>{t("click",a)}}}}),wa=["stroke-width","stroke-linecap","stroke-linejoin"],Ca=ne("path",{d:"M33.072 33.071c6.248-6.248 6.248-16.379 0-22.627-6.249-6.249-16.38-6.249-22.628 0-6.248 6.248-6.248 16.379 0 22.627 6.248 6.248 16.38 6.248 22.628 0Zm0 0 8.485 8.485"},null,-1),Sa=[Ca];function Ea(e,t,n,r,o,i){return P(),Y("svg",{viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor",class:N(e.cls),style:ke(e.innerStyle),"stroke-width":e.strokeWidth,"stroke-linecap":e.strokeLinecap,"stroke-linejoin":e.strokeLinejoin,onClick:t[0]||(t[0]=(...a)=>e.onClick&&e.onClick(...a))},Sa,14,wa)}var Kt=K(ya,[["render",Ea]]);const ur=Object.assign(Kt,{install:(e,t)=>{var n;const r=(n=t==null?void 0:t.iconPrefix)!=null?n:"";e.component(r+Kt.name,Kt)}}),co=Symbol("ArcoButtonGroup"),_a=R({name:"Button",components:{IconLoading:Tn},props:{type:{type:String},shape:{type:String},status:{type:String},size:{type:String},long:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},disabled:{type:Boolean},htmlType:{type:String,default:"button"},autofocus:{type:Boolean,default:!1},href:String},emits:{click:e=>!0},setup(e,{emit:t}){const{size:n,disabled:r}=_e(e),o=X("btn"),i=Ee(co,void 0),a=h(()=>{var v;return(v=n.value)!=null?v:i==null?void 0:i.size}),l=h(()=>!!(r.value||i!=null&&i.disabled)),{mergedSize:s,mergedDisabled:u}=uo({size:a,disabled:l}),{mergedSize:d}=Bt(s),c=h(()=>{var v,y,S,_,m,w;return[o,`${o}-${(y=(v=e.type)!=null?v:i==null?void 0:i.type)!=null?y:"secondary"}`,`${o}-shape-${(_=(S=e.shape)!=null?S:i==null?void 0:i.shape)!=null?_:"square"}`,`${o}-size-${d.value}`,`${o}-status-${(w=(m=e.status)!=null?m:i==null?void 0:i.status)!=null?w:"normal"}`,{[`${o}-long`]:e.long,[`${o}-loading`]:e.loading,[`${o}-disabled`]:u.value,[`${o}-link`]:ut(e.href)}]});return{prefixCls:o,cls:c,mergedDisabled:u,handleClick:v=>{if(e.disabled||e.loading){v.preventDefault();return}t("click",v)}}}}),ka=["href"],$a=["type","disabled","autofocus"];function Oa(e,t,n,r,o,i){const a=ie("icon-loading");return e.href?(P(),Y("a",{key:0,class:N([e.cls,{[`${e.prefixCls}-only-icon`]:e.$slots.icon&&!e.$slots.default}]),href:e.mergedDisabled||e.loading?void 0:e.href,onClick:t[0]||(t[0]=(...l)=>e.handleClick&&e.handleClick(...l))},[e.loading||e.$slots.icon?(P(),Y("span",{key:0,class:N(`${e.prefixCls}-icon`)},[e.loading?(P(),re(a,{key:0,spin:"true"})):U(e.$slots,"icon",{key:1})],2)):be("v-if",!0),U(e.$slots,"default")],10,ka)):(P(),Y("button",{key:1,class:N([e.cls,{[`${e.prefixCls}-only-icon`]:e.$slots.icon&&!e.$slots.default}]),type:e.htmlType,disabled:e.mergedDisabled,autofocus:e.autofocus,onClick:t[1]||(t[1]=(...l)=>e.handleClick&&e.handleClick(...l))},[e.loading||e.$slots.icon?(P(),Y("span",{key:0,class:N(`${e.prefixCls}-icon`)},[e.loading?(P(),re(a,{key:0,spin:!0})):U(e.$slots,"icon",{key:1})],2)):be("v-if",!0),U(e.$slots,"default")],10,$a))}var Qt=K(_a,[["render",Oa]]);const La=R({name:"ButtonGroup",props:{type:{type:String},status:{type:String},shape:{type:String},size:{type:String},disabled:{type:Boolean}},setup(e){const{type:t,size:n,status:r,disabled:o,shape:i}=_e(e),a=X("btn-group");return dt(co,Se({type:t,size:n,shape:i,status:r,disabled:o})),{prefixCls:a}}});function ja(e,t,n,r,o,i){return P(),Y("div",{class:N(e.prefixCls)},[U(e.$slots,"default")],2)}var en=K(La,[["render",ja]]);const Fa=Object.assign(Qt,{Group:en,install:(e,t)=>{ht(e,t);const n=vt(t);e.component(n+Qt.name,Qt),e.component(n+en.name,en)}});var tn=R({name:"InputSearch",props:{searchButton:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},size:{type:String},buttonText:{type:String},buttonProps:{type:Object}},emits:{search:(e,t)=>!0},setup(e,{emit:t,slots:n}){const{size:r}=_e(e),o=X("input-search"),{mergedSize:i}=Bt(r),a=B(),l=c=>{a.value.inputRef&&t("search",a.value.inputRef.value,c)},s=()=>{var c;return T(Fn,null,[e.loading?T(Tn,null,null):T(An,{onClick:l},{default:()=>[T(ur,null,null)]}),(c=n.suffix)==null?void 0:c.call(n)])},u=()=>{var c;let f={};return e.buttonText||n["button-default"]||n["button-icon"]?f={default:(c=n["button-default"])!=null?c:e.buttonText?()=>e.buttonText:void 0,icon:n["button-icon"]}:f={icon:()=>T(ur,null,null)},T(Fa,Me({type:"primary",class:`${o}-btn`,disabled:e.disabled,size:i.value,loading:e.loading},e.buttonProps,{onClick:l}),f)};return{inputRef:a,render:()=>T(st,{ref:a,class:o,size:i.value,disabled:e.disabled},{prepend:n.prepend,prefix:n.prefix,suffix:e.searchButton?n.suffix:s,append:e.searchButton?u:n.append})}},methods:{focus(){var e;(e=this.inputRef)==null||e.focus()},blur(){var e;(e=this.inputRef)==null||e.blur()}},render(){return this.render()}});const Pa=R({name:"IconEye",props:{size:{type:[Number,String]},strokeWidth:{type:Number,default:4},strokeLinecap:{type:String,default:"butt",validator:e=>["butt","round","square"].includes(e)},strokeLinejoin:{type:String,default:"miter",validator:e=>["arcs","bevel","miter","miter-clip","round"].includes(e)},rotate:Number,spin:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const n=X("icon"),r=h(()=>[n,`${n}-eye`,{[`${n}-spin`]:e.spin}]),o=h(()=>{const a={};return e.size&&(a.fontSize=de(e.size)?`${e.size}px`:e.size),e.rotate&&(a.transform=`rotate(${e.rotate}deg)`),a});return{cls:r,innerStyle:o,onClick:a=>{t("click",a)}}}}),xa=["stroke-width","stroke-linecap","stroke-linejoin"],Ma=ne("path",{"clip-rule":"evenodd",d:"M24 37c6.627 0 12.627-4.333 18-13-5.373-8.667-11.373-13-18-13-6.627 0-12.627 4.333-18 13 5.373 8.667 11.373 13 18 13Z"},null,-1),Va=ne("path",{d:"M29 24a5 5 0 1 1-10 0 5 5 0 0 1 10 0Z"},null,-1),Aa=[Ma,Va];function Ta(e,t,n,r,o,i){return P(),Y("svg",{viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor",class:N(e.cls),style:ke(e.innerStyle),"stroke-width":e.strokeWidth,"stroke-linecap":e.strokeLinecap,"stroke-linejoin":e.strokeLinejoin,onClick:t[0]||(t[0]=(...a)=>e.onClick&&e.onClick(...a))},Aa,14,xa)}var nn=K(Pa,[["render",Ta]]);const Ba=Object.assign(nn,{install:(e,t)=>{var n;const r=(n=t==null?void 0:t.iconPrefix)!=null?n:"";e.component(r+nn.name,nn)}}),Ia=R({name:"IconEyeInvisible",props:{size:{type:[Number,String]},strokeWidth:{type:Number,default:4},strokeLinecap:{type:String,default:"butt",validator:e=>["butt","round","square"].includes(e)},strokeLinejoin:{type:String,default:"miter",validator:e=>["arcs","bevel","miter","miter-clip","round"].includes(e)},rotate:Number,spin:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const n=X("icon"),r=h(()=>[n,`${n}-eye-invisible`,{[`${n}-spin`]:e.spin}]),o=h(()=>{const a={};return e.size&&(a.fontSize=de(e.size)?`${e.size}px`:e.size),e.rotate&&(a.transform=`rotate(${e.rotate}deg)`),a});return{cls:r,innerStyle:o,onClick:a=>{t("click",a)}}}}),za=["stroke-width","stroke-linecap","stroke-linejoin"],Na=ne("path",{d:"M14 14.5c-2.69 2-5.415 5.33-8 9.5 5.373 8.667 11.373 13 18 13 3.325 0 6.491-1.09 9.5-3.271M17.463 12.5C19 11 21.75 11 24 11c6.627 0 12.627 4.333 18 13-1.766 2.848-3.599 5.228-5.5 7.14"},null,-1),Da=ne("path",{d:"M29 24a5 5 0 1 1-10 0 5 5 0 0 1 10 0ZM6.852 7.103l34.294 34.294"},null,-1),Wa=[Na,Da];function Ra(e,t,n,r,o,i){return P(),Y("svg",{viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor",class:N(e.cls),style:ke(e.innerStyle),"stroke-width":e.strokeWidth,"stroke-linecap":e.strokeLinecap,"stroke-linejoin":e.strokeLinejoin,onClick:t[0]||(t[0]=(...a)=>e.onClick&&e.onClick(...a))},Wa,14,za)}var rn=K(Ia,[["render",Ra]]);const Ha=Object.assign(rn,{install:(e,t)=>{var n;const r=(n=t==null?void 0:t.iconPrefix)!=null?n:"";e.component(r+rn.name,rn)}});function Ga(e){const t=B(e);return[t,r=>{t.value=r}]}function Za(e,t){const{value:n}=_e(t),[r,o]=Ga(lt(n.value)?e:n.value);return We(n,a=>{lt(a)&&o(void 0)}),[h(()=>lt(n.value)?r.value:n.value),o,r]}const Ya=R({name:"InputPassword",components:{IconEye:Ba,IconEyeInvisible:Ha,AIconHover:An,AInput:st},props:{visibility:{type:Boolean,default:void 0},defaultVisibility:{type:Boolean,default:!0},invisibleButton:{type:Boolean,default:!0}},emits:["visibility-change","update:visibility"],setup(e,{emit:t}){const{visibility:n,defaultVisibility:r}=_e(e),o=B(),i=()=>{s(!a.value)},[a,l]=Za(r.value,Se({value:n})),s=u=>{u!==a.value&&(t("visibility-change",u),t("update:visibility",u),l(u))};return{inputRef:o,mergedVisible:a,handleInvisible:i}},methods:{focus(){var e;(e=this.inputRef)==null||e.focus()},blur(){var e;(e=this.inputRef)==null||e.blur()}}});function Xa(e,t,n,r,o,i){const a=ie("icon-eye"),l=ie("icon-eye-invisible"),s=ie("a-icon-hover"),u=ie("a-input");return P(),re(u,{ref:"inputRef",type:e.mergedVisible?"password":"text"},Yr({_:2},[e.$slots.prepend?{name:"prepend",fn:oe(()=>[U(e.$slots,"prepend")])}:void 0,e.$slots.prefix?{name:"prefix",fn:oe(()=>[U(e.$slots,"prefix")])}:void 0,e.invisibleButton||e.$slots.suffix?{name:"suffix",fn:oe(()=>[e.invisibleButton?(P(),re(s,{key:0,onClick:e.handleInvisible,onMousedown:t[0]||(t[0]=bn(()=>{},["prevent"])),onMouseup:t[1]||(t[1]=bn(()=>{},["prevent"]))},{default:oe(()=>[e.mergedVisible?(P(),re(l,{key:1})):(P(),re(a,{key:0}))]),_:1},8,["onClick"])):be("v-if",!0),U(e.$slots,"suffix")])}:void 0,e.$slots.append?{name:"append",fn:oe(()=>[U(e.$slots,"append")])}:void 0]),1032,["type"])}var on=K(Ya,[["render",Xa]]);const qa=R({name:"InputGroup",setup(){return{prefixCls:X("input-group")}}});function Ja(e,t,n,r,o,i){return P(),Y("div",{class:N(e.prefixCls)},[U(e.$slots,"default")],2)}var an=K(qa,[["render",Ja]]);const cd=Object.assign(st,{Search:tn,Password:on,Group:an,install:(e,t)=>{ht(e,t);const n=vt(t);e.component(n+st.name,st),e.component(n+an.name,an),e.component(n+tn.name,tn),e.component(n+on.name,on)}});var Ua=Object.defineProperty,cr=Object.getOwnPropertySymbols,Ka=Object.prototype.hasOwnProperty,Qa=Object.prototype.propertyIsEnumerable,dr=(e,t,n)=>t in e?Ua(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,it=(e,t)=>{for(var n in t||(t={}))Ka.call(t,n)&&dr(e,n,t[n]);if(cr)for(var n of cr(t))Qa.call(t,n)&&dr(e,n,t[n]);return e};const el=()=>{const{height:e,width:t}=ao();return{width:Math.min(t,window.innerWidth),height:Math.min(e,window.innerHeight)}},fr=(e,t)=>{var n,r;const o=e.getBoundingClientRect();return{top:o.top,bottom:o.bottom,left:o.left,right:o.right,scrollTop:o.top-t.top,scrollBottom:o.bottom-t.top,scrollLeft:o.left-t.left,scrollRight:o.right-t.left,width:(n=e.offsetWidth)!=null?n:e.clientWidth,height:(r=e.offsetHeight)!=null?r:e.clientHeight}},tl=e=>{switch(e){case"top":case"tl":case"tr":return"top";case"bottom":case"bl":case"br":return"bottom";case"left":case"lt":case"lb":return"left";case"right":case"rt":case"rb":return"right";default:return"top"}},Ct=(e,t)=>{switch(t){case"top":switch(e){case"bottom":return"top";case"bl":return"tl";case"br":return"tr";default:return e}case"bottom":switch(e){case"top":return"bottom";case"tl":return"bl";case"tr":return"br";default:return e}case"left":switch(e){case"right":return"left";case"rt":return"lt";case"rb":return"lb";default:return e}case"right":switch(e){case"left":return"right";case"lt":return"rt";case"lb":return"rb";default:return e}default:return e}},nl=(e,t,{containerRect:n,triggerRect:r,popupRect:o,offset:i,translate:a})=>{const l=tl(e),s=el(),u={top:n.top+t.top,bottom:s.height-(n.top+t.top+o.height),left:n.left+t.left,right:s.width-(n.left+t.left+o.width)};let d=e;if(l==="top"&&u.top<0)if(r.top>o.height)t.top=-n.top;else{const c=at("bottom",r,o,{offset:i,translate:a});s.height-(n.top+c.top+o.height)>0&&(d=Ct(e,"bottom"),t.top=c.top)}if(l==="bottom"&&u.bottom<0)if(s.height-r.bottom>o.height)t.top=-n.top+(s.height-o.height);else{const c=at("top",r,o,{offset:i,translate:a});n.top+c.top>0&&(d=Ct(e,"top"),t.top=c.top)}if(l==="left"&&u.left<0)if(r.left>o.width)t.left=-n.left;else{const c=at("right",r,o,{offset:i,translate:a});s.width-(n.left+c.left+o.width)>0&&(d=Ct(e,"right"),t.left=c.left)}if(l==="right"&&u.right<0)if(s.width-r.right>o.width)t.left=-n.left+(s.width-o.width);else{const c=at("left",r,o,{offset:i,translate:a});n.left+c.left>0&&(d=Ct(e,"left"),t.left=c.left)}return(l==="top"||l==="bottom")&&(u.left<0?t.left=-n.left:u.right<0&&(t.left=-n.left+(s.width-o.width))),(l==="left"||l==="right")&&(u.top<0?t.top=-n.top:u.bottom<0&&(t.top=-n.top+(s.height-o.height))),{popupPosition:t,position:d}},at=(e,t,n,{offset:r=0,translate:o=[0,0]}={})=>{var i;const a=(i=He(o)?o:o[e])!=null?i:[0,0];switch(e){case"top":return{left:t.scrollLeft+Math.round(t.width/2)-Math.round(n.width/2)+a[0],top:t.scrollTop-n.height-r+a[1]};case"tl":return{left:t.scrollLeft+a[0],top:t.scrollTop-n.height-r+a[1]};case"tr":return{left:t.scrollRight-n.width+a[0],top:t.scrollTop-n.height-r+a[1]};case"bottom":return{left:t.scrollLeft+Math.round(t.width/2)-Math.round(n.width/2)+a[0],top:t.scrollBottom+r+a[1]};case"bl":return{left:t.scrollLeft+a[0],top:t.scrollBottom+r+a[1]};case"br":return{left:t.scrollRight-n.width+a[0],top:t.scrollBottom+r+a[1]};case"left":return{left:t.scrollLeft-n.width-r+a[0],top:t.scrollTop+Math.round(t.height/2)-Math.round(n.height/2)+a[1]};case"lt":return{left:t.scrollLeft-n.width-r+a[0],top:t.scrollTop+a[1]};case"lb":return{left:t.scrollLeft-n.width-r+a[0],top:t.scrollBottom-n.height+a[1]};case"right":return{left:t.scrollRight+r+a[0],top:t.scrollTop+Math.round(t.height/2)-Math.round(n.height/2)+a[1]};case"rt":return{left:t.scrollRight+r+a[0],top:t.scrollTop+a[1]};case"rb":return{left:t.scrollRight+r+a[0],top:t.scrollBottom-n.height+a[1]};default:return{left:0,top:0}}},rl=e=>{let t="0";["top","bottom"].includes(e)?t="50%":["left","lt","lb","tr","br"].includes(e)&&(t="100%");let n="0";return["left","right"].includes(e)?n="50%":["top","tl","tr","lb","rb"].includes(e)&&(n="100%"),`${t} ${n}`},ol=(e,t,n,r,{offset:o=0,translate:i=[0,0],customStyle:a={},autoFitPosition:l=!1}={})=>{let s=e,u=at(e,n,r,{offset:o,translate:i});if(l){const c=nl(e,u,{containerRect:t,popupRect:r,triggerRect:n,offset:o,translate:i});u=c.popupPosition,s=c.position}return{style:it({left:`${u.left}px`,top:`${u.top}px`},a),position:s}},il=(e,t,n,{customStyle:r={}})=>{if(["top","tl","tr","bottom","bl","br"].includes(e)){let i=Math.abs(t.scrollLeft+t.width/2-n.scrollLeft);return i>n.width-8&&(t.width>n.width?i=n.width/2:i=n.width-8),["top","tl","tr"].includes(e)?it({left:`${i}px`,bottom:"0",transform:"translate(-50%,50%) rotate(45deg)"},r):it({left:`${i}px`,top:"0",transform:"translate(-50%,-50%) rotate(45deg)"},r)}let o=Math.abs(t.scrollTop+t.height/2-n.scrollTop);return o>n.height-8&&(t.height>n.height?o=n.height/2:o=n.height-8),["left","lt","lb"].includes(e)?it({top:`${o}px`,right:"0",transform:"translate(50%,-50%) rotate(45deg)"},r):it({top:`${o}px`,left:"0",transform:"translate(-50%,-50%) rotate(45deg)"},r)},al=e=>e.scrollHeight>e.offsetHeight||e.scrollWidth>e.offsetWidth,vr=e=>{var t;const n=[];let r=e;for(;r&&r!==document.documentElement;)al(r)&&n.push(r),r=(t=r.parentElement)!=null?t:void 0;return n},fo=()=>{const e={},t=B(),n=()=>{const r=oo(e.value);r!==t.value&&(t.value=r)};return Ie(()=>n()),Pn(()=>n()),{children:e,firstElement:t}};var En=R({name:"ResizeObserver",props:{watchOnUpdated:Boolean},emits:["resize"],setup(e,{emit:t,slots:n}){const{children:r,firstElement:o}=fo();let i;const a=s=>{s&&(i=new Qr(u=>{const d=u[0];t("resize",d)}),i.observe(s))},l=()=>{i&&(i.disconnect(),i=null)};return We(o,s=>{i&&l(),s&&a(s)}),ft(()=>{i&&l()}),()=>{var s;return r.value=(s=n.default)==null?void 0:s.call(n),r.value}}});function ll(e,t){const n=B(e[t]);return Pn(()=>{const r=e[t];n.value!==r&&(n.value=r)}),n}const hr=Symbol("ArcoTrigger"),sl=1e3,ul=5e3,cl=1;class dl{constructor(){this.popupStack={popup:new Set,dialog:new Set,message:new Set},this.getNextZIndex=t=>(t==="message"?Array.from(this.popupStack.message).pop()||ul:Array.from(this.popupStack.popup).pop()||sl)+cl,this.add=t=>{const n=this.getNextZIndex(t);return this.popupStack[t].add(n),t==="dialog"&&this.popupStack.popup.add(n),n},this.delete=(t,n)=>{this.popupStack[n].delete(t),n==="dialog"&&this.popupStack.popup.delete(t)},this.isLastDialog=t=>this.popupStack.dialog.size>1?t===Array.from(this.popupStack.dialog).pop():!0}}const ln=new dl;function fl(e,{visible:t,runOnMounted:n}={}){const r=B(0),o=()=>{r.value=ln.add(e)},i=()=>{ln.delete(r.value,e)},a=()=>e==="dialog"?ln.isLastDialog(r.value):!1;return We(()=>t==null?void 0:t.value,l=>{l?o():i()},{immediate:!0}),n&&(Ie(()=>{o()}),ft(()=>{i()})),{zIndex:No(r),open:o,close:i,isLastDialog:a}}const vl=({elementRef:e,onResize:t})=>{let n;return{createResizeObserver:()=>{e.value&&(n=new Qr(i=>{const a=i[0];tt(t)&&t(a)}),n.observe(e.value))},destroyResizeObserver:()=>{n&&(n.disconnect(),n=null)}}};var hl=R({name:"ClientOnly",setup(e,{slots:t}){const n=B(!1);return Ie(()=>n.value=!0),()=>{var r;return n.value?(r=t.default)==null?void 0:r.call(t):null}}});const pl=({popupContainer:e,visible:t,defaultContainer:n="body",documentContainer:r})=>{const o=B(e.value),i=B(),a=()=>{const l=Qn(e.value),s=l?e.value:n,u=l??(r?document.documentElement:Qn(n));s!==o.value&&(o.value=s),u!==i.value&&(i.value=u)};return Ie(()=>a()),We(t,l=>{o.value!==e.value&&l&&a()}),{teleportContainer:o,containerRef:i}};var ml=Object.defineProperty,gl=Object.defineProperties,bl=Object.getOwnPropertyDescriptors,pr=Object.getOwnPropertySymbols,yl=Object.prototype.hasOwnProperty,wl=Object.prototype.propertyIsEnumerable,mr=(e,t,n)=>t in e?ml(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Cl=(e,t)=>{for(var n in t||(t={}))yl.call(t,n)&&mr(e,n,t[n]);if(pr)for(var n of pr(t))wl.call(t,n)&&mr(e,n,t[n]);return e},Sl=(e,t)=>gl(e,bl(t));const El=["onClick","onMouseenter","onMouseleave","onFocusin","onFocusout","onContextmenu"];var sn=R({name:"Trigger",inheritAttrs:!1,props:{popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},trigger:{type:[String,Array],default:"hover"},position:{type:String,default:"bottom"},disabled:{type:Boolean,default:!1},popupOffset:{type:Number,default:0},popupTranslate:{type:[Array,Object]},showArrow:{type:Boolean,default:!1},alignPoint:{type:Boolean,default:!1},popupHoverStay:{type:Boolean,default:!0},blurToClose:{type:Boolean,default:!0},clickToClose:{type:Boolean,default:!0},clickOutsideToClose:{type:Boolean,default:!0},unmountOnClose:{type:Boolean,default:!0},contentClass:{type:[String,Array,Object]},contentStyle:{type:Object},arrowClass:{type:[String,Array,Object]},arrowStyle:{type:Object},popupStyle:{type:Object},animationName:{type:String,default:"fade-in"},duration:{type:[Number,Object]},mouseEnterDelay:{type:Number,default:100},mouseLeaveDelay:{type:Number,default:100},focusDelay:{type:Number,default:0},autoFitPopupWidth:{type:Boolean,default:!1},autoFitPopupMinWidth:{type:Boolean,default:!1},autoFixPosition:{type:Boolean,default:!0},popupContainer:{type:[String,Object]},updateAtScroll:{type:Boolean,default:!1},autoFitTransformOrigin:{type:Boolean,default:!1},hideEmpty:{type:Boolean,default:!1},openedClass:{type:[String,Array,Object]},autoFitPosition:{type:Boolean,default:!0},renderToBody:{type:Boolean,default:!0},preventFocus:{type:Boolean,default:!1},scrollToClose:{type:Boolean,default:!1},scrollToCloseDistance:{type:Number,default:0}},emits:{"update:popupVisible":e=>!0,popupVisibleChange:e=>!0,show:()=>!0,hide:()=>!0,resize:()=>!0},setup(e,{emit:t,slots:n,attrs:r}){const{popupContainer:o}=_e(e),i=X("trigger"),a=h(()=>lo(r,El)),l=Ee(Mt,void 0),s=h(()=>[].concat(e.trigger)),u=new Set,d=Ee(hr,void 0),{children:c,firstElement:f}=fo(),v=B(),y=B(e.defaultPopupVisible),S=B(e.position),_=B({}),m=B({}),w=B({}),k=B(),x=B({top:0,left:0});let A=null,H=null;const $=h(()=>{var g;return(g=e.popupVisible)!=null?g:y.value}),{teleportContainer:C,containerRef:D}=pl({popupContainer:o,visible:$,documentContainer:!0}),{zIndex:z}=fl("popup",{visible:$});let te=0,q=!1,O=!1;const se=()=>{te&&(window.clearTimeout(te),te=0)},ae=g=>{if(e.alignPoint){const{pageX:j,pageY:ue}=g;x.value={top:ue,left:j}}},Q=()=>{if(!f.value||!v.value||!D.value)return;const g=D.value.getBoundingClientRect(),j=e.alignPoint?{top:x.value.top,bottom:x.value.top,left:x.value.left,right:x.value.left,scrollTop:x.value.top,scrollBottom:x.value.top,scrollLeft:x.value.left,scrollRight:x.value.left,width:0,height:0}:fr(f.value,g),ue=()=>fr(v.value,g),Ye=ue(),{style:ze,position:Xe}=ol(e.position,g,j,Ye,{offset:e.popupOffset,translate:e.popupTranslate,customStyle:e.popupStyle,autoFitPosition:e.autoFitPosition});e.autoFitTransformOrigin&&(m.value={transformOrigin:rl(Xe)}),e.autoFitPopupMinWidth?ze.minWidth=`${j.width}px`:e.autoFitPopupWidth&&(ze.width=`${j.width}px`),S.value!==Xe&&(S.value=Xe),_.value=ze,e.showArrow&&Ke(()=>{w.value=il(Xe,j,ue(),{customStyle:e.arrowStyle})})},J=(g,j)=>{if(g===$.value&&te===0)return;const ue=()=>{y.value=g,t("update:popupVisible",g),t("popupVisibleChange",g),g&&Ke(()=>{Q()})};g||(A=null,H=null),j?(se(),g!==$.value&&(te=window.setTimeout(ue,j))):ue()},L=g=>{var j;(j=r.onClick)==null||j.call(r,g),!(e.disabled||$.value&&!e.clickToClose)&&(s.value.includes("click")?(ae(g),J(!$.value)):s.value.includes("contextMenu")&&$.value&&J(!1))},M=g=>{var j;(j=r.onMouseenter)==null||j.call(r,g),!(e.disabled||!s.value.includes("hover"))&&(ae(g),J(!0,e.mouseEnterDelay))},p=g=>{d==null||d.onMouseenter(g),M(g)},E=g=>{var j;(j=r.onMouseleave)==null||j.call(r,g),!(e.disabled||!s.value.includes("hover"))&&J(!1,e.mouseLeaveDelay)},F=g=>{d==null||d.onMouseleave(g),E(g)},W=g=>{var j;(j=r.onFocusin)==null||j.call(r,g),!(e.disabled||!s.value.includes("focus"))&&J(!0,e.focusDelay)},le=g=>{var j;(j=r.onFocusout)==null||j.call(r,g),!(e.disabled||!s.value.includes("focus"))&&e.blurToClose&&J(!1)},ve=g=>{var j;(j=r.onContextmenu)==null||j.call(r,g),!(e.disabled||!s.value.includes("contextMenu")||$.value&&!e.clickToClose)&&(ae(g),J(!$.value),g.preventDefault())};dt(hr,Se({onMouseenter:p,onMouseleave:F,addChildRef:g=>{u.add(g),d==null||d.addChildRef(g)},removeChildRef:g=>{u.delete(g),d==null||d.removeChildRef(g)}}));const he=()=>{Kn(document.documentElement,"mousedown",Pe),q=!1},we=ll(n,"content"),Ge=h(()=>{var g;return e.hideEmpty&&Oi((g=we.value)==null?void 0:g.call(we))}),Pe=g=>{var j,ue,Ye;if(!((j=f.value)!=null&&j.contains(g.target)||(ue=v.value)!=null&&ue.contains(g.target))){for(const ze of u)if((Ye=ze.value)!=null&&Ye.contains(g.target))return;he(),J(!1)}},Ze=(g,j)=>{const[ue,Ye]=g,{scrollTop:ze,scrollLeft:Xe}=j;return Math.abs(ze-ue)>=e.scrollToCloseDistance||Math.abs(Xe-Ye)>=e.scrollToCloseDistance},b=Un(g=>{if($.value)if(e.scrollToClose||l!=null&&l.scrollToClose){const j=g.target;A||(A=[j.scrollTop,j.scrollLeft]),Ze(A,j)?J(!1):Q()}else Q()}),V=()=>{Kn(window,"scroll",ee),O=!1},ee=Un(g=>{const j=g.target.documentElement;H||(H=[j.scrollTop,j.scrollLeft]),Ze(H,j)&&(J(!1),V())}),me=()=>{$.value&&Q()},ot=()=>{me(),t("resize")},Nt=g=>{e.preventFocus&&g.preventDefault()};d==null||d.addChildRef(v);const Dt=h(()=>$.value?e.openedClass:void 0);let Le;We($,g=>{if(e.clickOutsideToClose&&(!g&&q?he():g&&!q&&(Gt(document.documentElement,"mousedown",Pe),q=!0)),(e.scrollToClose||l!=null&&l.scrollToClose)&&(Gt(window,"scroll",ee),O=!0),e.updateAtScroll||l!=null&&l.updateAtScroll){if(g){Le=vr(f.value);for(const j of Le)j.addEventListener("scroll",b)}else if(Le){for(const j of Le)j.removeEventListener("scroll",b);Le=void 0}}g&&(Wt.value=!0)}),We(()=>[e.autoFitPopupWidth,e.autoFitPopupMinWidth],()=>{$.value&&Q()});const{createResizeObserver:Ao,destroyResizeObserver:To}=vl({elementRef:D,onResize:me});Ie(()=>{if(Ao(),$.value&&(Q(),e.clickOutsideToClose&&!q&&(Gt(document.documentElement,"mousedown",Pe),q=!0),e.updateAtScroll||l!=null&&l.updateAtScroll)){Le=vr(f.value);for(const g of Le)g.addEventListener("scroll",b)}}),Pn(()=>{$.value&&Q()}),Do(()=>{J(!1)}),ft(()=>{if(d==null||d.removeChildRef(v),To(),q&&he(),O&&V(),Le){for(const g of Le)g.removeEventListener("scroll",b);Le=void 0}});const Wt=B($.value),bt=B(!1),Hn=()=>{bt.value=!0},Bo=()=>{bt.value=!1,$.value&&t("show")},Io=()=>{bt.value=!1,$.value||(Wt.value=!1,t("hide"))};return()=>{var g,j;return c.value=(j=(g=n.default)==null?void 0:g.call(n))!=null?j:[],to(c.value,{class:Dt.value,onClick:L,onMouseenter:M,onMouseleave:E,onFocusin:W,onFocusout:le,onContextmenu:ve}),T(Fn,null,[e.autoFixPosition?T(En,{onResize:ot},{default:()=>[c.value]}):c.value,T(hl,null,{default:()=>[T(Wo,{to:C.value,disabled:!e.renderToBody},{default:()=>[(!e.unmountOnClose||$.value||Wt.value)&&!Ge.value&&T(En,{onResize:me},{default:()=>[T("div",Me({ref:v,class:[`${i}-popup`,`${i}-position-${S.value}`],style:Sl(Cl({},_.value),{zIndex:z.value,pointerEvents:bt.value?"none":"auto"}),"trigger-placement":S.value,onMouseenter:p,onMouseleave:F,onMousedown:Nt},a.value),[T(yn,{name:e.animationName,duration:e.duration,appear:!0,onBeforeEnter:Hn,onAfterEnter:Bo,onBeforeLeave:Hn,onAfterLeave:Io},{default:()=>{var ue;return[Ro(T("div",{class:`${i}-popup-wrapper`,style:m.value},[T("div",{class:[`${i}-content`,e.contentClass],style:e.contentStyle},[(ue=n.content)==null?void 0:ue.call(n)]),e.showArrow&&T("div",{ref:k,class:[`${i}-arrow`,e.arrowClass],style:w.value},null)]),[[Ho,$.value]])]}})])]})]})]})])}}});const _l=Object.assign(sn,{install:(e,t)=>{ht(e,t);const n=vt(t);e.component(n+sn.name,sn)}}),gr=(e,t)=>{if(!e||!t)return;t=t.replace(/\[(\w+)\]/g,".$1");const n=t.split(".");if(n.length===0)return;let r=e;for(let o=0;o<n.length;o++){if(!Be(r)&&!He(r)||!n[o])return;if(o!==n.length-1)r=r[n[o]];else return r[n[o]]}},br=(e,t,n,{addPath:r}={})=>{if(!e||!t)return;t=t.replace(/\[(\w+)\]/g,".$1");const o=t.split(".");if(o.length===0)return;let i=e;for(let a=0;a<o.length;a++){if(!Be(i)&&!He(i)||!o[a])return;a!==o.length-1?(r&&lt(i[o[a]])&&(i[o[a]]={}),i=i[o[a]]):i[o[a]]=n}};var kl=Object.defineProperty,$l=Object.defineProperties,Ol=Object.getOwnPropertyDescriptors,yr=Object.getOwnPropertySymbols,Ll=Object.prototype.hasOwnProperty,jl=Object.prototype.propertyIsEnumerable,wr=(e,t,n)=>t in e?kl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Fl=(e,t)=>{for(var n in t||(t={}))Ll.call(t,n)&&wr(e,n,t[n]);if(yr)for(var n of yr(t))jl.call(t,n)&&wr(e,n,t[n]);return e},Pl=(e,t)=>$l(e,Ol(t));const jt=["xxl","xl","lg","md","sm","xs"],St={xs:"(max-width: 575px)",sm:"(min-width: 576px)",md:"(min-width: 768px)",lg:"(min-width: 992px)",xl:"(min-width: 1200px)",xxl:"(min-width: 1600px)"};let Ne=[],xl=-1,Et={};const Cr={matchHandlers:{},dispatch(e,t){return Et=e,Ne.length<1?!1:(Ne.forEach(n=>{n.func(Et,t)}),!0)},subscribe(e){Ne.length===0&&this.register();const t=(++xl).toString();return Ne.push({token:t,func:e}),e(Et,null),t},unsubscribe(e){Ne=Ne.filter(t=>t.token!==e),Ne.length===0&&this.unregister()},unregister(){Object.keys(St).forEach(e=>{const t=St[e];if(!t)return;const n=this.matchHandlers[t];n&&n.mql&&n.listener&&(n.mql.removeEventListener?n.mql.removeEventListener("change",n.listener):n.mql.removeListener(n.listener))})},register(){Object.keys(St).forEach(e=>{const t=St[e];if(!t)return;const n=({matches:o})=>{this.dispatch(Pl(Fl({},Et),{[e]:o}),e)},r=window.matchMedia(t);r.addEventListener?r.addEventListener("change",n):r.addListener(n),this.matchHandlers[t]={mql:r,listener:n},n(r)})}};function Sr(e){return Be(e)}function _n(e,t,n=!1){const r=B({xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0}),o=h(()=>{let a=t;if(Sr(e.value))for(let l=0;l<jt.length;l++){const s=jt[l];if((r.value[s]||s==="xs"&&n)&&e.value[s]!==void 0){a=e.value[s];break}}else a=e.value;return a});let i="";return Ie(()=>{i=Cr.subscribe(a=>{Sr(e.value)&&(r.value=a)})}),Go(()=>{i&&Cr.unsubscribe(i)}),o}function vo(e){return e===Object(e)&&Object.keys(e).length!==0}function Ml(e,t){t===void 0&&(t="auto");var n="scrollBehavior"in document.body.style;e.forEach(function(r){var o=r.el,i=r.top,a=r.left;o.scroll&&n?o.scroll({top:i,left:a,behavior:t}):(o.scrollTop=i,o.scrollLeft=a)})}function Vl(e){return e===!1?{block:"end",inline:"nearest"}:vo(e)?e:{block:"start",inline:"nearest"}}function Al(e,t){var n=e.isConnected||e.ownerDocument.documentElement.contains(e);if(vo(t)&&typeof t.behavior=="function")return t.behavior(n?nr(e,t):[]);if(n){var r=Vl(t);return Ml(nr(e,r),r.behavior)}}const Er=["success","warning","error","validating"],Tl=e=>{let t="";for(const n of Object.keys(e)){const r=e[n];r&&(!t||Er.indexOf(r)>Er.indexOf(t))&&(t=e[n])}return t},Bl=e=>{const t=[];for(const n of Object.keys(e)){const r=e[n];r&&t.push(r)}return t},ho=(e,t)=>{const n=t.replace(/[[.]/g,"_").replace(/\]/g,"");return e?`${e}-${n}`:`${n}`};var Il=Object.defineProperty,_r=Object.getOwnPropertySymbols,zl=Object.prototype.hasOwnProperty,Nl=Object.prototype.propertyIsEnumerable,kr=(e,t,n)=>t in e?Il(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Dl=(e,t)=>{for(var n in t||(t={}))zl.call(t,n)&&kr(e,n,t[n]);if(_r)for(var n of _r(t))Nl.call(t,n)&&kr(e,n,t[n]);return e};const Wl=R({name:"Form",props:{model:{type:Object,required:!0},layout:{type:String,default:"horizontal"},size:{type:String},labelColProps:{type:Object,default:()=>({span:5,offset:0})},wrapperColProps:{type:Object,default:()=>({span:19,offset:0})},labelColStyle:Object,wrapperColStyle:Object,labelAlign:{type:String,default:"right"},disabled:{type:Boolean,default:void 0},rules:{type:Object},autoLabelWidth:{type:Boolean,default:!1},id:{type:String},scrollToFirstError:{type:Boolean,default:!1}},emits:{submit:(e,t)=>!0,submitSuccess:(e,t)=>!0,submitFailed:(e,t)=>!0},setup(e,{emit:t}){const n=X("form"),r=B(),{id:o,model:i,layout:a,disabled:l,labelAlign:s,labelColProps:u,wrapperColProps:d,labelColStyle:c,wrapperColStyle:f,size:v,rules:y}=_e(e),{mergedSize:S}=Bt(v),_=h(()=>e.layout==="horizontal"&&e.autoLabelWidth),m=[],w=[],k=Se({}),x=h(()=>Math.max(...Object.values(k))),A=L=>{L&&L.field&&m.push(L)},H=L=>{L&&L.field&&m.splice(m.indexOf(L),1)},$=L=>{m.forEach(M=>{L[M.field]&&M.setField(L[M.field])})},C=(L,M)=>{M&&k[M]!==L&&(k[M]=L)},D=L=>{L&&delete k[L]},z=L=>{const M=L?[].concat(L):[];m.forEach(p=>{(M.length===0||M.includes(p.field))&&p.resetField()})},te=L=>{const M=L?[].concat(L):[];m.forEach(p=>{(M.length===0||M.includes(p.field))&&p.clearValidate()})},q=(L,M)=>{const E=(r.value||document.body).querySelector(`#${ho(e.id,L)}`);E&&Al(E,Dl({behavior:"smooth",block:"nearest",scrollMode:"if-needed"},M))},O=L=>{const M=qo(e.scrollToFirstError)?void 0:e.scrollToFirstError;q(L,M)},se=L=>{const M=[];return m.forEach(p=>{M.push(p.validate())}),Promise.all(M).then(p=>{const E={};let F=!1;return p.forEach(W=>{W&&(F=!0,E[W.field]=W)}),F&&e.scrollToFirstError&&O(Object.keys(E)[0]),tt(L)&&L(F?E:void 0),F?E:void 0})},ae=(L,M)=>{const p=[];for(const E of m)(He(L)&&L.includes(E.field)||L===E.field)&&p.push(E.validate());return Promise.all(p).then(E=>{const F={};let W=!1;return E.forEach(le=>{le&&(W=!0,F[le.field]=le)}),W&&e.scrollToFirstError&&O(Object.keys(F)[0]),tt(M)&&M(W?F:void 0),W?F:void 0})},Q=L=>{const M=[];m.forEach(p=>{M.push(p.validate())}),Promise.all(M).then(p=>{const E={};let F=!1;p.forEach(W=>{W&&(F=!0,E[W.field]=W)}),F?(e.scrollToFirstError&&O(Object.keys(E)[0]),t("submitFailed",{values:i.value,errors:E},L)):t("submitSuccess",i.value,L),t("submit",{values:i.value,errors:F?E:void 0},L)})};return dt(Bn,Se({id:o,layout:a,disabled:l,labelAlign:s,labelColProps:u,wrapperColProps:d,labelColStyle:c,wrapperColStyle:f,model:i,size:S,rules:y,fields:m,touchedFields:w,addField:A,removeField:H,validateField:ae,setLabelWidth:C,removeLabelWidth:D,maxLabelWidth:x,autoLabelWidth:_})),{cls:h(()=>[n,`${n}-layout-${e.layout}`,`${n}-size-${S.value}`,{[`${n}-auto-label-width`]:e.autoLabelWidth}]),formRef:r,handleSubmit:Q,innerValidate:se,innerValidateField:ae,innerResetFields:z,innerClearValidate:te,innerSetFields:$,innerScrollToField:q}},methods:{validate(e){return this.innerValidate(e)},validateField(e,t){return this.innerValidateField(e,t)},resetFields(e){return this.innerResetFields(e)},clearValidate(e){return this.innerClearValidate(e)},setFields(e){return this.innerSetFields(e)},scrollToField(e){return this.innerScrollToField(e)}}});function Rl(e,t,n,r,o,i){return P(),Y("form",{ref:"formRef",class:N(e.cls),onSubmit:t[0]||(t[0]=bn((...a)=>e.handleSubmit&&e.handleSubmit(...a),["prevent"]))},[U(e.$slots,"default")],34)}var un=K(Wl,[["render",Rl]]),rt=Object.prototype.toString;function It(e){return rt.call(e)==="[object Array]"}function Ve(e){return rt.call(e)==="[object Object]"}function kn(e){return rt.call(e)==="[object String]"}function Hl(e){return rt.call(e)==="[object Number]"&&e===e}function Gl(e){return rt.call(e)==="[object Boolean]"}function $n(e){return rt.call(e)==="[object Function]"}function Zl(e){return Ve(e)&&Object.keys(e).length===0}function De(e){return e==null||e===""}function po(e){return It(e)&&!e.length}var In=function(e,t){if(typeof e!="object"||typeof t!="object")return e===t;if($n(e)&&$n(t))return e===t||e.toString()===t.toString();if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var n in e){var r=In(e[n],t[n]);if(!r)return!1}return!0},zn=function(e,t){var n=Object.assign({},e);return Object.keys(t||{}).forEach(function(r){var o=n[r],i=t==null?void 0:t[r];n[r]=Ve(o)?Object.assign(Object.assign({},o),i):i||o}),n},Yl=function(e,t){for(var n=t.split("."),r=e,o=0;o<n.length;o++)if(r=r&&r[n[o]],r===void 0)return r;return r},Ae="#{field} is not a #{type} type",Xl={required:"#{field} is required",type:{ip:Ae,email:Ae,url:Ae,string:Ae,number:Ae,array:Ae,object:Ae,boolean:Ae},number:{min:"`#{value}` is not greater than `#{min}`",max:"`#{value}` is not less than `#{max}`",equal:"`#{value}` is not equal to `#{equal}`",range:"`#{value}` is not in range `#{min} ~ #{max}`",positive:"`#{value}` is not a positive number",negative:"`#{value}` is not a negative number"},string:{maxLength:"#{field} cannot be longer than #{maxLength} characters",minLength:"#{field} must be at least #{minLength} characters",length:"#{field} must be exactly #{length} characters",match:"`#{value}` does not match pattern #{pattern}",uppercase:"`#{value}` must be all uppercase",lowercase:"`#{value}` must be all lowercased"},array:{length:"#{field} must be exactly #{length} in length",minLength:"#{field} cannot be less than #{minLength} in length",maxLength:"#{field} cannot be greater than #{maxLength} in length",includes:"#{field} is not includes #{includes}",deepEqual:"#{field} is not deep equal with #{deepEqual}",empty:"#{field} is not an empty array"},object:{deepEqual:"#{field} is not deep equal to expected value",hasKeys:"#{field} does not contain required fields",empty:"#{field} is not an empty object"},boolean:{true:"Expect true but got `#{value}`",false:"Expect false but got `#{value}`"}},$e=function(t,n){var r=this;this.getValidateMsg=function(o,i){i===void 0&&(i={});var a=Object.assign(Object.assign({},i),{value:r.obj,field:r.field,type:r.type}),l=Yl(r.validateMessages,o);return $n(l)?l(a):kn(l)?l.replace(/\#\{.+?\}/g,function(s){var u=s.slice(2,-1);if(u in a){if(Ve(a[u])||It(a[u]))try{return JSON.stringify(a[u])}catch{return a[u]}return String(a[u])}return s}):l},Ve(n)&&kn(t)&&n.trim?this.obj=t.trim():Ve(n)&&n.ignoreEmptyString&&t===""?this.obj=void 0:this.obj=t,this.message=n.message,this.type=n.type,this.error=null,this.field=n.field||n.type,this.validateMessages=zn(Xl,n.validateMessages)},zt={not:{configurable:!0},isRequired:{configurable:!0},end:{configurable:!0}};zt.not.get=function(){return this._not=!this._not,this};zt.isRequired.get=function(){if(De(this.obj)||po(this.obj)){var e=this.getValidateMsg("required");this.error={value:this.obj,type:this.type,requiredError:!0,message:this.message||(Ve(e)?e:(this._not?"[NOT MODE]:":"")+e)}}return this};zt.end.get=function(){return this.error};$e.prototype.addError=function(t){!this.error&&t&&(this.error={value:this.obj,type:this.type,message:this.message||(Ve(t)?t:(this._not?"[NOT MODE]:":"")+t)})};$e.prototype.validate=function(t,n){var r=this._not?t:!t;return r&&this.addError(n),this};$e.prototype.collect=function(t){t&&t(this.error)};Object.defineProperties($e.prototype,zt);var ql=function(e){function t(r,o){e.call(this,r,Object.assign(Object.assign({},o),{type:"string"})),this.validate(o&&o.strict?kn(this.obj):!0,this.getValidateMsg("type.string"))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={uppercase:{configurable:!0},lowercase:{configurable:!0}};return t.prototype.maxLength=function(o){return this.obj?this.validate(this.obj.length<=o,this.getValidateMsg("string.maxLength",{maxLength:o})):this},t.prototype.minLength=function(o){return this.obj?this.validate(this.obj.length>=o,this.getValidateMsg("string.minLength",{minLength:o})):this},t.prototype.length=function(o){return this.obj?this.validate(this.obj.length===o,this.getValidateMsg("string.length",{length:o})):this},t.prototype.match=function(o){var i=o instanceof RegExp;return i&&(o.lastIndex=0),this.validate(this.obj===void 0||i&&o.test(this.obj),this.getValidateMsg("string.match",{pattern:o}))},n.uppercase.get=function(){return this.obj?this.validate(this.obj.toUpperCase()===this.obj,this.getValidateMsg("string.uppercase")):this},n.lowercase.get=function(){return this.obj?this.validate(this.obj.toLowerCase()===this.obj,this.getValidateMsg("string.lowercase")):this},Object.defineProperties(t.prototype,n),t}($e),Jl=function(e){function t(r,o){e.call(this,r,Object.assign(Object.assign({},o),{type:"number"})),this.validate(o&&o.strict?Hl(this.obj):!0,this.getValidateMsg("type.number"))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={positive:{configurable:!0},negative:{configurable:!0}};return t.prototype.min=function(o){return De(this.obj)?this:this.validate(this.obj>=o,this.getValidateMsg("number.min",{min:o}))},t.prototype.max=function(o){return De(this.obj)?this:this.validate(this.obj<=o,this.getValidateMsg("number.max",{max:o}))},t.prototype.equal=function(o){return De(this.obj)?this:this.validate(this.obj===o,this.getValidateMsg("number.equal",{equal:o}))},t.prototype.range=function(o,i){return De(this.obj)?this:this.validate(this.obj>=o&&this.obj<=i,this.getValidateMsg("number.range",{min:o,max:i}))},n.positive.get=function(){return De(this.obj)?this:this.validate(this.obj>0,this.getValidateMsg("number.positive"))},n.negative.get=function(){return De(this.obj)?this:this.validate(this.obj<0,this.getValidateMsg("number.negative"))},Object.defineProperties(t.prototype,n),t}($e),Ul=function(e){function t(r,o){e.call(this,r,Object.assign(Object.assign({},o),{type:"array"})),this.validate(o&&o.strict?It(this.obj):!0,this.getValidateMsg("type.array",{value:this.obj,type:this.type}))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={empty:{configurable:!0}};return t.prototype.length=function(o){return this.obj?this.validate(this.obj.length===o,this.getValidateMsg("array.length",{value:this.obj,length:o})):this},t.prototype.minLength=function(o){return this.obj?this.validate(this.obj.length>=o,this.getValidateMsg("array.minLength",{value:this.obj,minLength:o})):this},t.prototype.maxLength=function(o){return this.obj?this.validate(this.obj.length<=o,this.getValidateMsg("array.maxLength",{value:this.obj,maxLength:o})):this},t.prototype.includes=function(o){var i=this;return this.obj?this.validate(o.every(function(a){return i.obj.indexOf(a)!==-1}),this.getValidateMsg("array.includes",{value:this.obj,includes:o})):this},t.prototype.deepEqual=function(o){return this.obj?this.validate(In(this.obj,o),this.getValidateMsg("array.deepEqual",{value:this.obj,deepEqual:o})):this},n.empty.get=function(){return this.validate(po(this.obj),this.getValidateMsg("array.empty",{value:this.obj}))},Object.defineProperties(t.prototype,n),t}($e),Kl=function(e){function t(r,o){e.call(this,r,Object.assign(Object.assign({},o),{type:"object"})),this.validate(o&&o.strict?Ve(this.obj):!0,this.getValidateMsg("type.object"))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={empty:{configurable:!0}};return t.prototype.deepEqual=function(o){return this.obj?this.validate(In(this.obj,o),this.getValidateMsg("object.deepEqual",{deepEqual:o})):this},t.prototype.hasKeys=function(o){var i=this;return this.obj?this.validate(o.every(function(a){return i.obj[a]}),this.getValidateMsg("object.hasKeys",{keys:o})):this},n.empty.get=function(){return this.validate(Zl(this.obj),this.getValidateMsg("object.empty"))},Object.defineProperties(t.prototype,n),t}($e),Ql=function(e){function t(r,o){e.call(this,r,Object.assign(Object.assign({},o),{type:"boolean"})),this.validate(o&&o.strict?Gl(this.obj):!0,this.getValidateMsg("type.boolean"))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={true:{configurable:!0},false:{configurable:!0}};return n.true.get=function(){return this.validate(this.obj===!0,this.getValidateMsg("boolean.true"))},n.false.get=function(){return this.validate(this.obj===!1,this.getValidateMsg("boolean.false"))},Object.defineProperties(t.prototype,n),t}($e),es=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,ts=new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i"),ns=/^(2(5[0-5]{1}|[0-4]\d{1})|[0-1]?\d{1,2})(\.(2(5[0-5]{1}|[0-4]\d{1})|[0-1]?\d{1,2})){3}$/,rs=function(e){function t(r,o){e.call(this,r,Object.assign(Object.assign({},o),{type:"type"}))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={email:{configurable:!0},url:{configurable:!0},ip:{configurable:!0}};return n.email.get=function(){return this.type="email",this.validate(this.obj===void 0||es.test(this.obj),this.getValidateMsg("type.email"))},n.url.get=function(){return this.type="url",this.validate(this.obj===void 0||ts.test(this.obj),this.getValidateMsg("type.url"))},n.ip.get=function(){return this.type="ip",this.validate(this.obj===void 0||ns.test(this.obj),this.getValidateMsg("type.ip"))},Object.defineProperties(t.prototype,n),t}($e),os=function(e){function t(r,o){e.call(this,r,Object.assign(Object.assign({},o),{type:"custom"}))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={validate:{configurable:!0}};return n.validate.get=function(){var r=this;return function(o,i){var a;if(o)return a=o(r.obj,r.addError.bind(r)),a&&a.then?(i&&a.then(function(){i&&i(r.error)},function(l){console.error(l)}),[a,r]):(i&&i(r.error),r.error)}},Object.defineProperties(t.prototype,n),t}($e),Ft=function(e,t){return new mo(e,Object.assign({field:"value"},t))};Ft.globalConfig={};Ft.setGlobalConfig=function(e){Ft.globalConfig=e||{}};var mo=function(t,n){var r=Ft.globalConfig,o=Object.assign(Object.assign(Object.assign({},r),n),{validateMessages:zn(r.validateMessages,n.validateMessages)});this.string=new ql(t,o),this.number=new Jl(t,o),this.array=new Ul(t,o),this.object=new Kl(t,o),this.boolean=new Ql(t,o),this.type=new rs(t,o),this.custom=new os(t,o)},Nn=function(t,n){n===void 0&&(n={}),this.schema=t,this.options=n};Nn.prototype.messages=function(t){this.options=Object.assign(Object.assign({},this.options),{validateMessages:zn(this.options.validateMessages,t)})};Nn.prototype.validate=function(t,n){var r=this;if(!Ve(t))return;var o=[],i=null;function a(l,s){i||(i={}),(!i[l]||s.requiredError)&&(i[l]=s)}this.schema&&Object.keys(this.schema).forEach(function(l){if(It(r.schema[l]))for(var s=function(c){var f=r.schema[l][c],v=f.type,y=f.message;if(!v&&!f.validator)throw"You must specify a type to field "+l+"!";var S=Object.assign(Object.assign({},r.options),{message:y,field:l});"ignoreEmptyString"in f&&(S.ignoreEmptyString=f.ignoreEmptyString),"strict"in f&&(S.strict=f.strict);var _=new mo(t[l],S),m=_.type[v]||null;if(!m)if(f.validator){m=_.custom.validate(f.validator),Object.prototype.toString.call(m)==="[object Array]"&&m[0].then?o.push({function:m[0],_this:m[1],key:l}):m&&a(l,m);return}else m=_[v];if(Object.keys(f).forEach(function(w){f.required&&(m=m.isRequired),w!=="message"&&m[w]&&f[w]&&typeof m[w]=="object"&&(m=m[w]),m[w]&&f[w]!==void 0&&typeof m[w]=="function"&&(m=m[w](f[w]))}),m.collect(function(w){w&&a(l,w)}),i)return"break"},u=0;u<r.schema[l].length;u++){var d=s(u);if(d==="break")break}}),o.length>0?Promise.all(o.map(function(l){return l.function})).then(function(){o.forEach(function(l){l._this.error&&a(l.key,l._this.error)}),n&&n(i)}):n&&n(i)};const go=Symbol("RowContextInjectionKey"),is=R({name:"Row",props:{gutter:{type:[Number,Object,Array],default:0},justify:{type:String,default:"start"},align:{type:String,default:"start"},div:{type:Boolean},wrap:{type:Boolean,default:!0}},setup(e){const{gutter:t,align:n,justify:r,div:o,wrap:i}=_e(e),a=X("row"),l=h(()=>({[`${a}`]:!o.value,[`${a}-nowrap`]:!i.value,[`${a}-align-${n.value}`]:n.value,[`${a}-justify-${r.value}`]:r.value})),s=h(()=>Array.isArray(t.value)?t.value[0]:t.value),u=h(()=>Array.isArray(t.value)?t.value[1]:0),d=_n(s,0),c=_n(u,0),f=h(()=>{const y={};if((d.value||c.value)&&!o.value){const S=-d.value/2,_=-c.value/2;S&&(y.marginLeft=`${S}px`,y.marginRight=`${S}px`),_&&(y.marginTop=`${_}px`,y.marginBottom=`${_}px`)}return y}),v=h(()=>[d.value,c.value]);return dt(go,Se({gutter:v,div:o})),{classNames:l,styles:f}}});function as(e,t,n,r,o,i){return P(),Y("div",{class:N(e.classNames),style:ke(e.styles)},[U(e.$slots,"default")],6)}var ls=K(is,[["render",as]]);function ss(e){return h(()=>{const{val:n,key:r,xs:o,sm:i,md:a,lg:l,xl:s,xxl:u}=e.value;if(!o&&!i&&!a&&!l&&!s&&!u)return n;const d={};return jt.forEach(c=>{const f=e.value[c];de(f)?d[c]=f:Be(f)&&de(f[r])&&(d[c]=f[r])}),d})}var us=Object.defineProperty,$r=Object.getOwnPropertySymbols,cs=Object.prototype.hasOwnProperty,ds=Object.prototype.propertyIsEnumerable,Or=(e,t,n)=>t in e?us(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,cn=(e,t)=>{for(var n in t||(t={}))cs.call(t,n)&&Or(e,n,t[n]);if($r)for(var n of $r(t))ds.call(t,n)&&Or(e,n,t[n]);return e};function fs(e){if(ut(e)&&(["initial","auto","none"].includes(e)||/^\d+$/.test(e))||de(e))return e;if(ut(e)&&/^\d+(px|em|rem|%)$/.test(e))return`0 0 ${e}`}const vs=R({name:"Col",props:{span:{type:Number,default:24},offset:{type:Number},order:{type:Number},xs:{type:[Number,Object]},sm:{type:[Number,Object]},md:{type:[Number,Object]},lg:{type:[Number,Object]},xl:{type:[Number,Object]},xxl:{type:[Number,Object]},flex:{type:[Number,String]}},setup(e){const t=X("col"),n=Ee(go,{}),r=h(()=>fs(e.flex)),o=h(()=>{const{div:c}=n,{span:f,offset:v,order:y,xs:S,sm:_,md:m,lg:w,xl:k,xxl:x}=e,A={[`${t}`]:!c,[`${t}-order-${y}`]:y,[`${t}-${f}`]:!c&&!S&&!_&&!m&&!w&&!k&&!x,[`${t}-offset-${v}`]:v&&v>0},H={xs:S,sm:_,md:m,lg:w,xl:k,xxl:x};return Object.keys(H).forEach($=>{const C=H[$];C&&de(C)?A[`${t}-${$}-${C}`]=!0:C&&Be(C)&&(A[`${t}-${$}-${C.span}`]=C.span,A[`${t}-${$}-offset-${C.offset}`]=C.offset,A[`${t}-${$}-order-${C.order}`]=C.order)}),A}),i=h(()=>r.value?t:o.value),a=h(()=>{const{gutter:c,div:f}=n,v={};if(Array.isArray(c)&&!f){const y=c[0]&&c[0]/2||0,S=c[1]&&c[1]/2||0;y&&(v.paddingLeft=`${y}px`,v.paddingRight=`${y}px`),S&&(v.paddingTop=`${S}px`,v.paddingBottom=`${S}px`)}return v}),l=h(()=>r.value?{flex:r.value}:{}),s=h(()=>so(e,jt)),u=ss(h(()=>cn({val:e.span,key:"span"},s.value))),d=_n(u,24,!0);return{visible:h(()=>!!d.value),classNames:i,styles:h(()=>cn(cn({},a.value),l.value))}}});function hs(e,t,n,r,o,i){return e.visible?(P(),Y("div",{key:0,class:N(e.classNames),style:ke(e.styles)},[U(e.$slots,"default")],6)):be("v-if",!0)}var ps=K(vs,[["render",hs]]),ms=Object.defineProperty,Lr=Object.getOwnPropertySymbols,gs=Object.prototype.hasOwnProperty,bs=Object.prototype.propertyIsEnumerable,jr=(e,t,n)=>t in e?ms(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Fr=(e,t)=>{for(var n in t||(t={}))gs.call(t,n)&&jr(e,n,t[n]);if(Lr)for(var n of Lr(t))bs.call(t,n)&&jr(e,n,t[n]);return e};const ys=R({name:"Tooltip",components:{Trigger:_l},props:{popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},content:String,position:{type:String,default:"top"},mini:{type:Boolean,default:!1},backgroundColor:{type:String},contentClass:{type:[String,Array,Object]},contentStyle:{type:Object},arrowClass:{type:[String,Array,Object]},arrowStyle:{type:Object},popupContainer:{type:[String,Object]}},emits:{"update:popupVisible":e=>!0,popupVisibleChange:e=>!0},setup(e,{emit:t}){const n=X("tooltip"),r=B(e.defaultPopupVisible),o=h(()=>{var d;return(d=e.popupVisible)!=null?d:r.value}),i=d=>{r.value=d,t("update:popupVisible",d),t("popupVisibleChange",d)},a=h(()=>[`${n}-content`,e.contentClass,{[`${n}-mini`]:e.mini}]),l=h(()=>{if(e.backgroundColor||e.contentStyle)return Fr({backgroundColor:e.backgroundColor},e.contentStyle)}),s=h(()=>[`${n}-popup-arrow`,e.arrowClass]),u=h(()=>{if(e.backgroundColor||e.arrowStyle)return Fr({backgroundColor:e.backgroundColor},e.arrowStyle)});return{prefixCls:n,computedPopupVisible:o,contentCls:a,computedContentStyle:l,arrowCls:s,computedArrowStyle:u,handlePopupVisibleChange:i}}});function ws(e,t,n,r,o,i){const a=ie("Trigger");return P(),re(a,{class:N(e.prefixCls),trigger:"hover",position:e.position,"popup-visible":e.computedPopupVisible,"popup-offset":10,"show-arrow":"","content-class":e.contentCls,"content-style":e.computedContentStyle,"arrow-class":e.arrowCls,"arrow-style":e.computedArrowStyle,"popup-container":e.popupContainer,"animation-name":"zoom-in-fade-out","auto-fit-transform-origin":"",role:"tooltip",onPopupVisibleChange:e.handlePopupVisibleChange},{content:oe(()=>[U(e.$slots,"content",{},()=>[Qe(et(e.content),1)])]),default:oe(()=>[U(e.$slots,"default")]),_:3},8,["class","position","popup-visible","content-class","content-style","arrow-class","arrow-style","popup-container","onPopupVisibleChange"])}var dn=K(ys,[["render",ws]]);const Cs=Object.assign(dn,{install:(e,t)=>{ht(e,t);const n=vt(t);e.component(n+dn.name,dn)}}),Ss=R({name:"IconQuestionCircle",props:{size:{type:[Number,String]},strokeWidth:{type:Number,default:4},strokeLinecap:{type:String,default:"butt",validator:e=>["butt","round","square"].includes(e)},strokeLinejoin:{type:String,default:"miter",validator:e=>["arcs","bevel","miter","miter-clip","round"].includes(e)},rotate:Number,spin:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const n=X("icon"),r=h(()=>[n,`${n}-question-circle`,{[`${n}-spin`]:e.spin}]),o=h(()=>{const a={};return e.size&&(a.fontSize=de(e.size)?`${e.size}px`:e.size),e.rotate&&(a.transform=`rotate(${e.rotate}deg)`),a});return{cls:r,innerStyle:o,onClick:a=>{t("click",a)}}}}),Es=["stroke-width","stroke-linecap","stroke-linejoin"],_s=ne("path",{d:"M42 24c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z"},null,-1),ks=ne("path",{d:"M24.006 31v4.008m0-6.008L24 28c0-3 3-4 4.78-6.402C30.558 19.195 28.288 15 23.987 15c-4.014 0-5.382 2.548-5.388 4.514v.465"},null,-1),$s=[_s,ks];function Os(e,t,n,r,o,i){return P(),Y("svg",{viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor",class:N(e.cls),style:ke(e.innerStyle),"stroke-width":e.strokeWidth,"stroke-linecap":e.strokeLinecap,"stroke-linejoin":e.strokeLinejoin,onClick:t[0]||(t[0]=(...a)=>e.onClick&&e.onClick(...a))},$s,14,Es)}var fn=K(Ss,[["render",Os]]);const Ls=Object.assign(fn,{install:(e,t)=>{var n;const r=(n=t==null?void 0:t.iconPrefix)!=null?n:"";e.component(r+fn.name,fn)}}),js=R({name:"FormItemLabel",components:{ResizeObserver:En,Tooltip:Cs,IconQuestionCircle:Ls},props:{required:{type:Boolean,default:!1},showColon:{type:Boolean,default:!1},component:{type:String,default:"label"},asteriskPosition:{type:String,default:"start"},tooltip:{type:String},attrs:Object},setup(){const e=X("form-item-label"),t=Ee(Bn,void 0),n=Zr(),r=B(),o=()=>{r.value&&de(r.value.offsetWidth)&&(t==null||t.setLabelWidth(r.value.offsetWidth,n==null?void 0:n.uid))};return Ie(()=>{r.value&&de(r.value.offsetWidth)&&(t==null||t.setLabelWidth(r.value.offsetWidth,n==null?void 0:n.uid))}),ft(()=>{t==null||t.removeLabelWidth(n==null?void 0:n.uid)}),{prefixCls:e,labelRef:r,handleResize:o}}}),Fs=ne("svg",{fill:"currentColor",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},[ne("path",{d:"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z"})],-1),Ps=[Fs],xs=ne("svg",{fill:"currentColor",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},[ne("path",{d:"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z"})],-1),Ms=[xs];function Vs(e,t,n,r,o,i){const a=ie("icon-question-circle"),l=ie("Tooltip"),s=ie("ResizeObserver");return P(),re(s,{onResize:e.handleResize},{default:oe(()=>[(P(),re(Zo(e.component),Me({ref:"labelRef",class:e.prefixCls},e.attrs),{default:oe(()=>[e.required&&e.asteriskPosition==="start"?(P(),Y("strong",{key:0,class:N(`${e.prefixCls}-required-symbol`)},Ps,2)):be("v-if",!0),U(e.$slots,"default"),e.tooltip?(P(),re(l,{key:1,content:e.tooltip},{default:oe(()=>[T(a,{class:N(`${e.prefixCls}-tooltip`)},null,8,["class"])]),_:1},8,["content"])):be("v-if",!0),e.required&&e.asteriskPosition==="end"?(P(),Y("strong",{key:2,class:N(`${e.prefixCls}-required-symbol`)},Ms,2)):be("v-if",!0),Qe(" "+et(e.showColon?":":""),1)]),_:3},16,["class"]))]),_:3},8,["onResize"])}var As=K(js,[["render",Vs]]);const Ts=R({name:"FormItemMessage",props:{error:{type:Array,default:()=>[]},help:String},setup(){return{prefixCls:X("form-item-message")}}});function Bs(e,t,n,r,o,i){return e.error.length>0?(P(!0),Y(Fn,{key:0},Yo(e.error,a=>(P(),re(yn,{key:a,name:"form-blink",appear:""},{default:oe(()=>[ne("div",{role:"alert",class:N([e.prefixCls])},et(a),3)]),_:2},1024))),128)):e.help||e.$slots.help?(P(),re(yn,{key:1,name:"form-blink",appear:""},{default:oe(()=>[ne("div",{class:N([e.prefixCls,`${e.prefixCls}-help`])},[U(e.$slots,"help",{},()=>[Qe(et(e.help),1)])],2)]),_:3})):be("v-if",!0)}var Is=K(Ts,[["render",Bs]]),zs=Object.defineProperty,Pt=Object.getOwnPropertySymbols,bo=Object.prototype.hasOwnProperty,yo=Object.prototype.propertyIsEnumerable,Pr=(e,t,n)=>t in e?zs(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,xr=(e,t)=>{for(var n in t||(t={}))bo.call(t,n)&&Pr(e,n,t[n]);if(Pt)for(var n of Pt(t))yo.call(t,n)&&Pr(e,n,t[n]);return e},Ns=(e,t)=>{var n={};for(var r in e)bo.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&Pt)for(var r of Pt(e))t.indexOf(r)<0&&yo.call(e,r)&&(n[r]=e[r]);return n};const Ds=R({name:"FormItem",components:{ArcoRow:ls,ArcoCol:ps,FormItemLabel:As,FormItemMessage:Is},props:{field:{type:String,default:""},label:String,tooltip:{type:String},showColon:{type:Boolean,default:!1},noStyle:{type:Boolean,default:!1},disabled:{type:Boolean,default:void 0},help:String,extra:String,required:{type:Boolean,default:!1},asteriskPosition:{type:String,default:"start"},rules:{type:[Object,Array]},validateStatus:{type:String},validateTrigger:{type:[String,Array],default:"change"},labelColProps:Object,wrapperColProps:Object,hideLabel:{type:Boolean,default:!1},hideAsterisk:{type:Boolean,default:!1},labelColStyle:Object,wrapperColStyle:Object,rowProps:Object,rowClass:[String,Array,Object],contentClass:[String,Array,Object],contentFlex:{type:Boolean,default:!0},mergeProps:{type:[Boolean,Function],default:!0},labelColFlex:{type:[Number,String]},feedback:{type:Boolean,default:!1},labelComponent:{type:String,default:"label"},labelAttrs:Object},setup(e){const t=X("form-item"),{field:n}=_e(e),r=Ee(Bn,{}),{autoLabelWidth:o,layout:i}=_e(r),{i18nMessage:a}=Qo(),l=h(()=>{var p;const E=xr({},(p=e.labelColProps)!=null?p:r.labelColProps);return e.labelColFlex?E.flex=e.labelColFlex:r.autoLabelWidth&&(E.flex=`${r.maxLabelWidth}px`),E}),s=h(()=>{var p;const E=xr({},(p=e.wrapperColProps)!=null?p:r.wrapperColProps);return n.value&&(E.id=ho(r.id,n.value)),(e.labelColFlex||r.autoLabelWidth)&&(E.flex="auto"),E}),u=h(()=>{var p;return(p=e.labelColStyle)!=null?p:r.labelColStyle}),d=h(()=>{var p;return(p=e.wrapperColStyle)!=null?p:r.wrapperColStyle}),c=gr(r.model,e.field),f=Se({}),v=Se({}),y=h(()=>Tl(f)),S=h(()=>Bl(v)),_=B(!1),m=h(()=>gr(r.model,e.field)),w=h(()=>{var p;return!!((p=e.disabled)!=null?p:r!=null&&r.disabled)}),k=h(()=>{var p;return(p=e.validateStatus)!=null?p:y.value}),x=h(()=>k.value==="error"),A=h(()=>{var p,E,F;const W=[].concat((F=(E=e.rules)!=null?E:(p=r==null?void 0:r.rules)==null?void 0:p[e.field])!=null?F:[]),le=W.some(ve=>ve.required);return e.required&&!le?[{required:!0}].concat(W):W}),H=h(()=>A.value.some(p=>p.required)),$=e.noStyle?Ee(Sn,void 0):void 0,C=(p,{status:E,message:F})=>{f[p]=E,v[p]=F,e.noStyle&&($==null||$.updateValidateState(p,{status:E,message:F}))},D=h(()=>e.feedback&&k.value?k.value:void 0),z=()=>{var p;if(_.value)return Promise.resolve();const E=A.value;if(!n.value||E.length===0)return y.value&&O(),Promise.resolve();const F=n.value,W=m.value;C(F,{status:"",message:""});const le=new Nn({[F]:E.map(ve=>{var G=Ns(ve,[]);return!G.type&&!G.validator&&(G.type="string"),G})},{ignoreEmptyString:!0,validateMessages:(p=a.value.form)==null?void 0:p.validateMessages});return new Promise(ve=>{le.validate({[F]:W},G=>{var pe;const he=!!(G!=null&&G[F]);C(F,{status:he?"error":"",message:(pe=G==null?void 0:G[F].message)!=null?pe:""});const we=he?{label:e.label,field:n.value,value:G[F].value,type:G[F].type,isRequiredError:!!G[F].requiredError,message:G[F].message}:void 0;ve(we)})})},te=h(()=>[].concat(e.validateTrigger)),q=h(()=>te.value.reduce((p,E)=>{switch(E){case"change":return p.onChange=()=>{z()},p;case"input":return p.onInput=()=>{Ke(()=>{z()})},p;case"focus":return p.onFocus=()=>{z()},p;case"blur":return p.onBlur=()=>{z()},p;default:return p}},{}));dt(Sn,Se({eventHandlers:q,size:r&&gn(r,"size"),disabled:w,error:x,feedback:D,updateValidateState:C}));const O=()=>{n.value&&C(n.value,{status:"",message:""})},Q=Se({field:n,disabled:w,error:x,validate:z,clearValidate:O,resetField:()=>{O(),_.value=!0,r!=null&&r.model&&n.value&&br(r.model,n.value,c),Ke(()=>{_.value=!1})},setField:p=>{var E,F;n.value&&(_.value=!0,"value"in p&&(r!=null&&r.model)&&n.value&&br(r.model,n.value,p.value),(p.status||p.message)&&C(n.value,{status:(E=p.status)!=null?E:"",message:(F=p.message)!=null?F:""}),Ke(()=>{_.value=!1}))}});Ie(()=>{var p;Q.field&&((p=r.addField)==null||p.call(r,Q))}),ft(()=>{var p;Q.field&&((p=r.removeField)==null||p.call(r,Q))});const J=h(()=>[t,`${t}-layout-${r.layout}`,{[`${t}-error`]:x.value,[`${t}-status-${k.value}`]:!!k.value},e.rowClass]),L=h(()=>[`${t}-label-col`,{[`${t}-label-col-left`]:r.labelAlign==="left",[`${t}-label-col-flex`]:r.autoLabelWidth||e.labelColFlex}]),M=h(()=>[`${t}-wrapper-col`,{[`${t}-wrapper-col-flex`]:!s.value}]);return{prefixCls:t,cls:J,isRequired:H,isError:x,finalMessage:S,mergedLabelCol:l,mergedWrapperCol:s,labelColCls:L,autoLabelWidth:o,layout:i,mergedLabelStyle:u,wrapperColCls:M,mergedWrapperStyle:d}}});function Ws(e,t,n,r,o,i){var a;const l=ie("FormItemLabel"),s=ie("ArcoCol"),u=ie("FormItemMessage"),d=ie("ArcoRow");return e.noStyle?U(e.$slots,"default",{key:0}):(P(),re(d,Me({key:1,class:[e.cls,{[`${e.prefixCls}-has-help`]:!!((a=e.$slots.help)!=null?a:e.help)}],wrap:!(e.labelColFlex||e.autoLabelWidth),div:e.layout!=="horizontal"||e.hideLabel},e.rowProps),{default:oe(()=>[e.hideLabel?be("v-if",!0):(P(),re(s,Me({key:0,class:e.labelColCls,style:e.mergedLabelStyle},e.mergedLabelCol),{default:oe(()=>[T(l,{required:e.hideAsterisk?!1:e.isRequired,"show-colon":e.showColon,"asterisk-position":e.asteriskPosition,component:e.labelComponent,attrs:e.labelAttrs,tooltip:e.tooltip},{default:oe(()=>[e.$slots.label||e.label?U(e.$slots,"label",{key:0},()=>[Qe(et(e.label),1)]):be("v-if",!0)]),_:3},8,["required","show-colon","asterisk-position","component","attrs","tooltip"])]),_:3},16,["class","style"])),T(s,Me({class:e.wrapperColCls,style:e.mergedWrapperStyle},e.mergedWrapperCol),{default:oe(()=>[ne("div",{class:N(`${e.prefixCls}-content-wrapper`)},[ne("div",{class:N([`${e.prefixCls}-content`,{[`${e.prefixCls}-content-flex`]:e.contentFlex},e.contentClass])},[U(e.$slots,"default")],2)],2),e.isError||e.$slots.help||e.help?(P(),re(u,{key:0,error:e.finalMessage,help:e.help},Yr({_:2},[e.$slots.help?{name:"help",fn:oe(()=>[U(e.$slots,"help")])}:void 0]),1032,["error","help"])):be("v-if",!0),e.$slots.extra||e.extra?(P(),Y("div",{key:1,class:N(`${e.prefixCls}-extra`)},[U(e.$slots,"extra",{},()=>[Qe(et(e.extra),1)])],2)):be("v-if",!0)]),_:3},16,["class","style"])]),_:3},16,["class","wrap","div"]))}var vn=K(Ds,[["render",Ws]]);const dd=Object.assign(un,{Item:vn,install:(e,t)=>{ht(e,t);const n=vt(t);e.component(n+un.name,un),e.component(n+vn.name,vn)}});var On=function(){return On=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},On.apply(this,arguments)};function je(e,t,n,r){function o(i){return i instanceof n?i:new n(function(a){a(i)})}return new(n||(n=Promise))(function(i,a){function l(d){try{u(r.next(d))}catch(c){a(c)}}function s(d){try{u(r.throw(d))}catch(c){a(c)}}function u(d){d.done?i(d.value):o(d.value).then(l,s)}u((r=r.apply(e,t||[])).next())})}function Fe(e,t){var n={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,o,i,a=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return a.next=l(0),a.throw=l(1),a.return=l(2),typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function l(u){return function(d){return s([u,d])}}function s(u){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(n=0)),n;)try{if(r=1,o&&(i=u[0]&2?o.return:u[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,u[1])).done)return i;switch(o=0,i&&(u=[u[0]&2,i.value]),u[0]){case 0:case 1:i=u;break;case 4:return n.label++,{value:u[1],done:!1};case 5:n.label++,o=u[1],u=[0];continue;case 7:u=n.ops.pop(),n.trys.pop();continue;default:if(i=n.trys,!(i=i.length>0&&i[i.length-1])&&(u[0]===6||u[0]===2)){n=0;continue}if(u[0]===3&&(!i||u[1]>i[0]&&u[1]<i[3])){n.label=u[1];break}if(u[0]===6&&n.label<i[1]){n.label=i[1],i=u;break}if(i&&n.label<i[2]){n.label=i[2],n.ops.push(u);break}i[2]&&n.ops.pop(),n.trys.pop();continue}u=t.call(e,n)}catch(d){u=[6,d],o=0}finally{r=i=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function wo(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var Co="4.6.2";function xt(e,t){return new Promise(function(n){return setTimeout(n,e,t)})}function Rs(){return new Promise(function(e){var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(null)})}function Hs(e,t){t===void 0&&(t=1/0);var n=window.requestIdleCallback;return n?new Promise(function(r){return n.call(window,function(){return r()},{timeout:t})}):xt(Math.min(e,t))}function So(e){return!!e&&typeof e.then=="function"}function Mr(e,t){try{var n=e();So(n)?n.then(function(r){return t(!0,r)},function(r){return t(!1,r)}):t(!0,n)}catch(r){t(!1,r)}}function Vr(e,t,n){return n===void 0&&(n=16),je(this,void 0,void 0,function(){var r,o,i,a;return Fe(this,function(l){switch(l.label){case 0:r=Array(e.length),o=Date.now(),i=0,l.label=1;case 1:return i<e.length?(r[i]=t(e[i],i),a=Date.now(),a>=o+n?(o=a,[4,Rs()]):[3,3]):[3,4];case 2:l.sent(),l.label=3;case 3:return++i,[3,1];case 4:return[2,r]}})})}function ct(e){return e.then(void 0,function(){}),e}function Gs(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return!0;return!1}function Zs(e,t){return!Gs(e,t)}function Dn(e){return parseInt(e)}function Ce(e){return parseFloat(e)}function xe(e,t){return typeof e=="number"&&isNaN(e)?t:e}function fe(e){return e.reduce(function(t,n){return t+(n?1:0)},0)}function Eo(e,t){if(t===void 0&&(t=1),Math.abs(t)>=1)return Math.round(e/t)*t;var n=1/t;return Math.round(e*n)/n}function Ys(e){for(var t,n,r="Unexpected syntax '".concat(e,"'"),o=/^\s*([a-z-]*)(.*)$/i.exec(e),i=o[1]||void 0,a={},l=/([.:#][\w-]+|\[.+?\])/gi,s=function(f,v){a[f]=a[f]||[],a[f].push(v)};;){var u=l.exec(o[2]);if(!u)break;var d=u[0];switch(d[0]){case".":s("class",d.slice(1));break;case"#":s("id",d.slice(1));break;case"[":{var c=/^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(d);if(c)s(c[1],(n=(t=c[4])!==null&&t!==void 0?t:c[5])!==null&&n!==void 0?n:"");else throw new Error(r);break}default:throw new Error(r)}}return[i,a]}function Xs(e){for(var t=new Uint8Array(e.length),n=0;n<e.length;n++){var r=e.charCodeAt(n);if(r>127)return new TextEncoder().encode(e);t[n]=r}return t}function Te(e,t){var n=e[0]>>>16,r=e[0]&65535,o=e[1]>>>16,i=e[1]&65535,a=t[0]>>>16,l=t[0]&65535,s=t[1]>>>16,u=t[1]&65535,d=0,c=0,f=0,v=0;v+=i+u,f+=v>>>16,v&=65535,f+=o+s,c+=f>>>16,f&=65535,c+=r+l,d+=c>>>16,c&=65535,d+=n+a,d&=65535,e[0]=d<<16|c,e[1]=f<<16|v}function ye(e,t){var n=e[0]>>>16,r=e[0]&65535,o=e[1]>>>16,i=e[1]&65535,a=t[0]>>>16,l=t[0]&65535,s=t[1]>>>16,u=t[1]&65535,d=0,c=0,f=0,v=0;v+=i*u,f+=v>>>16,v&=65535,f+=o*u,c+=f>>>16,f&=65535,f+=i*s,c+=f>>>16,f&=65535,c+=r*u,d+=c>>>16,c&=65535,c+=o*s,d+=c>>>16,c&=65535,c+=i*l,d+=c>>>16,c&=65535,d+=n*u+r*s+o*l+i*a,d&=65535,e[0]=d<<16|c,e[1]=f<<16|v}function qe(e,t){var n=e[0];t%=64,t===32?(e[0]=e[1],e[1]=n):t<32?(e[0]=n<<t|e[1]>>>32-t,e[1]=e[1]<<t|n>>>32-t):(t-=32,e[0]=e[1]<<t|n>>>32-t,e[1]=n<<t|e[1]>>>32-t)}function ge(e,t){t%=64,t!==0&&(t<32?(e[0]=e[1]>>>32-t,e[1]=e[1]<<t):(e[0]=e[1]<<t-32,e[1]=0))}function Z(e,t){e[0]^=t[0],e[1]^=t[1]}var qs=[4283543511,3981806797],Js=[3301882366,444984403];function Ar(e){var t=[0,e[0]>>>1];Z(e,t),ye(e,qs),t[1]=e[0]>>>1,Z(e,t),ye(e,Js),t[1]=e[0]>>>1,Z(e,t)}var _t=[2277735313,289559509],kt=[1291169091,658871167],Tr=[0,5],Us=[0,1390208809],Ks=[0,944331445];function Qs(e,t){var n=Xs(e);t=t||0;var r=[0,n.length],o=r[1]%16,i=r[1]-o,a=[0,t],l=[0,t],s=[0,0],u=[0,0],d;for(d=0;d<i;d=d+16)s[0]=n[d+4]|n[d+5]<<8|n[d+6]<<16|n[d+7]<<24,s[1]=n[d]|n[d+1]<<8|n[d+2]<<16|n[d+3]<<24,u[0]=n[d+12]|n[d+13]<<8|n[d+14]<<16|n[d+15]<<24,u[1]=n[d+8]|n[d+9]<<8|n[d+10]<<16|n[d+11]<<24,ye(s,_t),qe(s,31),ye(s,kt),Z(a,s),qe(a,27),Te(a,l),ye(a,Tr),Te(a,Us),ye(u,kt),qe(u,33),ye(u,_t),Z(l,u),qe(l,31),Te(l,a),ye(l,Tr),Te(l,Ks);s[0]=0,s[1]=0,u[0]=0,u[1]=0;var c=[0,0];switch(o){case 15:c[1]=n[d+14],ge(c,48),Z(u,c);case 14:c[1]=n[d+13],ge(c,40),Z(u,c);case 13:c[1]=n[d+12],ge(c,32),Z(u,c);case 12:c[1]=n[d+11],ge(c,24),Z(u,c);case 11:c[1]=n[d+10],ge(c,16),Z(u,c);case 10:c[1]=n[d+9],ge(c,8),Z(u,c);case 9:c[1]=n[d+8],Z(u,c),ye(u,kt),qe(u,33),ye(u,_t),Z(l,u);case 8:c[1]=n[d+7],ge(c,56),Z(s,c);case 7:c[1]=n[d+6],ge(c,48),Z(s,c);case 6:c[1]=n[d+5],ge(c,40),Z(s,c);case 5:c[1]=n[d+4],ge(c,32),Z(s,c);case 4:c[1]=n[d+3],ge(c,24),Z(s,c);case 3:c[1]=n[d+2],ge(c,16),Z(s,c);case 2:c[1]=n[d+1],ge(c,8),Z(s,c);case 1:c[1]=n[d],Z(s,c),ye(s,_t),qe(s,31),ye(s,kt),Z(a,s)}return Z(a,r),Z(l,r),Te(a,l),Te(l,a),Ar(a),Ar(l),Te(a,l),Te(l,a),("00000000"+(a[0]>>>0).toString(16)).slice(-8)+("00000000"+(a[1]>>>0).toString(16)).slice(-8)+("00000000"+(l[0]>>>0).toString(16)).slice(-8)+("00000000"+(l[1]>>>0).toString(16)).slice(-8)}function eu(e){var t;return On({name:e.name,message:e.message,stack:(t=e.stack)===null||t===void 0?void 0:t.split(`
`)},e)}function tu(e){return/^function\s.*?\{\s*\[native code]\s*}$/.test(String(e))}function nu(e){return typeof e!="function"}function ru(e,t){var n=ct(new Promise(function(r){var o=Date.now();Mr(e.bind(null,t),function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];var l=Date.now()-o;if(!i[0])return r(function(){return{error:i[1],duration:l}});var s=i[1];if(nu(s))return r(function(){return{value:s,duration:l}});r(function(){return new Promise(function(u){var d=Date.now();Mr(s,function(){for(var c=[],f=0;f<arguments.length;f++)c[f]=arguments[f];var v=l+Date.now()-d;if(!c[0])return u({error:c[1],duration:v});u({value:c[1],duration:v})})})})})}));return function(){return n.then(function(o){return o()})}}function ou(e,t,n,r){var o=Object.keys(e).filter(function(a){return Zs(n,a)}),i=ct(Vr(o,function(a){return ru(e[a],t)},r));return function(){return je(this,void 0,void 0,function(){var l,s,u,d,c;return Fe(this,function(f){switch(f.label){case 0:return[4,i];case 1:return l=f.sent(),[4,Vr(l,function(v){return ct(v())},r)];case 2:return s=f.sent(),[4,Promise.all(s)];case 3:for(u=f.sent(),d={},c=0;c<o.length;++c)d[o[c]]=u[c];return[2,d]}})})}}function _o(){var e=window,t=navigator;return fe(["MSCSSMatrix"in e,"msSetImmediate"in e,"msIndexedDB"in e,"msMaxTouchPoints"in t,"msPointerEnabled"in t])>=4}function iu(){var e=window,t=navigator;return fe(["msWriteProfilerMark"in e,"MSStream"in e,"msLaunchUri"in t,"msSaveBlob"in t])>=3&&!_o()}function pt(){var e=window,t=navigator;return fe(["webkitPersistentStorage"in t,"webkitTemporaryStorage"in t,(t.vendor||"").indexOf("Google")===0,"webkitResolveLocalFileSystemURL"in e,"BatteryManager"in e,"webkitMediaStream"in e,"webkitSpeechGrammar"in e])>=5}function Oe(){var e=window,t=navigator;return fe(["ApplePayError"in e,"CSSPrimitiveValue"in e,"Counter"in e,t.vendor.indexOf("Apple")===0,"RGBColor"in e,"WebKitMediaKeys"in e])>=4}function Wn(){var e=window,t=e.HTMLElement,n=e.Document;return fe(["safari"in e,!("ongestureend"in e),!("TouchEvent"in e),!("orientation"in e),t&&!("autocapitalize"in t.prototype),n&&"pointerLockElement"in n.prototype])>=4}function mt(){var e=window;return tu(e.print)&&String(e.browser)==="[object WebPageNamespace]"}function ko(){var e,t,n=window;return fe(["buildID"in navigator,"MozAppearance"in((t=(e=document.documentElement)===null||e===void 0?void 0:e.style)!==null&&t!==void 0?t:{}),"onmozfullscreenchange"in n,"mozInnerScreenX"in n,"CSSMozDocumentRule"in n,"CanvasCaptureMediaStream"in n])>=4}function au(){var e=window;return fe([!("MediaSettingsRange"in e),"RTCEncodedAudioFrame"in e,""+e.Intl=="[object Intl]",""+e.Reflect=="[object Reflect]"])>=3}function lu(){var e=window,t=e.URLPattern;return fe(["union"in Set.prototype,"Iterator"in e,t&&"hasRegExpGroups"in t.prototype,"RGB8"in WebGLRenderingContext.prototype])>=3}function su(){var e=window;return fe(["DOMRectList"in e,"RTCPeerConnectionIceEvent"in e,"SVGGeometryElement"in e,"ontransitioncancel"in e])>=3}function gt(){var e=window,t=navigator,n=e.CSS,r=e.HTMLButtonElement;return fe([!("getStorageUpdates"in t),r&&"popover"in r.prototype,"CSSCounterStyleRule"in e,n.supports("font-size-adjust: ex-height 0.5"),n.supports("text-transform: full-width")])>=4}function uu(){if(navigator.platform==="iPad")return!0;var e=screen,t=e.width/e.height;return fe(["MediaSource"in window,!!Element.prototype.webkitRequestFullscreen,t>.65&&t<1.53])>=2}function cu(){var e=document;return e.fullscreenElement||e.msFullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement||null}function du(){var e=document;return(e.exitFullscreen||e.msExitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen).call(e)}function Rn(){var e=pt(),t=ko(),n=window,r=navigator,o="connection";return e?fe([!("SharedWorker"in n),r[o]&&"ontypechange"in r[o],!("sinkId"in new Audio)])>=2:t?fe(["onorientationchange"in n,"orientation"in n,/android/i.test(r.appVersion)])>=2:!1}function fu(){var e=navigator,t=window,n=Audio.prototype,r=t.visualViewport;return fe(["srLatency"in n,"srChannelCount"in n,"devicePosture"in e,r&&"segments"in r,"getTextInformation"in Image.prototype])>=3}function vu(){return mu()?-4:hu()}function hu(){var e=window,t=e.OfflineAudioContext||e.webkitOfflineAudioContext;if(!t)return-2;if(pu())return-1;var n=4500,r=5e3,o=new t(1,r,44100),i=o.createOscillator();i.type="triangle",i.frequency.value=1e4;var a=o.createDynamicsCompressor();a.threshold.value=-50,a.knee.value=40,a.ratio.value=12,a.attack.value=0,a.release.value=.25,i.connect(a),a.connect(o.destination),i.start(0);var l=gu(o),s=l[0],u=l[1],d=ct(s.then(function(c){return bu(c.getChannelData(0).subarray(n))},function(c){if(c.name==="timeout"||c.name==="suspended")return-3;throw c}));return function(){return u(),d}}function pu(){return Oe()&&!Wn()&&!su()}function mu(){return Oe()&&gt()&&mt()||pt()&&fu()&&lu()}function gu(e){var t=3,n=500,r=500,o=5e3,i=function(){},a=new Promise(function(l,s){var u=!1,d=0,c=0;e.oncomplete=function(y){return l(y.renderedBuffer)};var f=function(){setTimeout(function(){return s(Br("timeout"))},Math.min(r,c+o-Date.now()))},v=function(){try{var y=e.startRendering();switch(So(y)&&ct(y),e.state){case"running":c=Date.now(),u&&f();break;case"suspended":document.hidden||d++,u&&d>=t?s(Br("suspended")):setTimeout(v,n);break}}catch(S){s(S)}};v(),i=function(){u||(u=!0,c>0&&f())}});return[a,i]}function bu(e){for(var t=0,n=0;n<e.length;++n)t+=Math.abs(e[n]);return t}function Br(e){var t=new Error(e);return t.name=e,t}function $o(e,t,n){var r,o,i;return n===void 0&&(n=50),je(this,void 0,void 0,function(){var a,l;return Fe(this,function(s){switch(s.label){case 0:a=document,s.label=1;case 1:return a.body?[3,3]:[4,xt(n)];case 2:return s.sent(),[3,1];case 3:l=a.createElement("iframe"),s.label=4;case 4:return s.trys.push([4,,10,11]),[4,new Promise(function(u,d){var c=!1,f=function(){c=!0,u()},v=function(_){c=!0,d(_)};l.onload=f,l.onerror=v;var y=l.style;y.setProperty("display","block","important"),y.position="absolute",y.top="0",y.left="0",y.visibility="hidden",t&&"srcdoc"in l?l.srcdoc=t:l.src="about:blank",a.body.appendChild(l);var S=function(){var _,m;c||(((m=(_=l.contentWindow)===null||_===void 0?void 0:_.document)===null||m===void 0?void 0:m.readyState)==="complete"?f():setTimeout(S,10))};S()})];case 5:s.sent(),s.label=6;case 6:return!((o=(r=l.contentWindow)===null||r===void 0?void 0:r.document)===null||o===void 0)&&o.body?[3,8]:[4,xt(n)];case 7:return s.sent(),[3,6];case 8:return[4,e(l,l.contentWindow)];case 9:return[2,s.sent()];case 10:return(i=l.parentNode)===null||i===void 0||i.removeChild(l),[7];case 11:return[2]}})})}function yu(e){for(var t=Ys(e),n=t[0],r=t[1],o=document.createElement(n??"div"),i=0,a=Object.keys(r);i<a.length;i++){var l=a[i],s=r[l].join(" ");l==="style"?wu(o.style,s):o.setAttribute(l,s)}return o}function wu(e,t){for(var n=0,r=t.split(";");n<r.length;n++){var o=r[n],i=/^\s*([\w-]+)\s*:\s*(.+?)(\s*!([\w-]+))?\s*$/.exec(o);if(i){var a=i[1],l=i[2],s=i[4];e.setProperty(a,l,s||"")}}}function Cu(){for(var e=window;;){var t=e.parent;if(!t||t===e)return!1;try{if(t.location.origin!==e.location.origin)return!0}catch(n){if(n instanceof Error&&n.name==="SecurityError")return!0;throw n}e=t}}var Su="mmMwWLliI0O&1",Eu="48px",Je=["monospace","sans-serif","serif"],Ir=["sans-serif-thin","ARNO PRO","Agency FB","Arabic Typesetting","Arial Unicode MS","AvantGarde Bk BT","BankGothic Md BT","Batang","Bitstream Vera Sans Mono","Calibri","Century","Century Gothic","Clarendon","EUROSTILE","Franklin Gothic","Futura Bk BT","Futura Md BT","GOTHAM","Gill Sans","HELV","Haettenschweiler","Helvetica Neue","Humanst521 BT","Leelawadee","Letter Gothic","Levenim MT","Lucida Bright","Lucida Sans","Menlo","MS Mincho","MS Outlook","MS Reference Specialty","MS UI Gothic","MT Extra","MYRIAD PRO","Marlett","Meiryo UI","Microsoft Uighur","Minion Pro","Monotype Corsiva","PMingLiU","Pristina","SCRIPTINA","Segoe UI Light","Serifa","SimHei","Small Fonts","Staccato222 BT","TRAJAN PRO","Univers CE 55 Medium","Vrinda","ZWAdobeF"];function _u(){var e=this;return $o(function(t,n){var r=n.document;return je(e,void 0,void 0,function(){var o,i,a,l,s,u,d,c,f,v,y,S;return Fe(this,function(_){for(o=r.body,o.style.fontSize=Eu,i=r.createElement("div"),i.style.setProperty("visibility","hidden","important"),a={},l={},s=function(m){var w=r.createElement("span"),k=w.style;return k.position="absolute",k.top="0",k.left="0",k.fontFamily=m,w.textContent=Su,i.appendChild(w),w},u=function(m,w){return s("'".concat(m,"',").concat(w))},d=function(){return Je.map(s)},c=function(){for(var m={},w=function(H){m[H]=Je.map(function($){return u(H,$)})},k=0,x=Ir;k<x.length;k++){var A=x[k];w(A)}return m},f=function(m){return Je.some(function(w,k){return m[k].offsetWidth!==a[w]||m[k].offsetHeight!==l[w]})},v=d(),y=c(),o.appendChild(i),S=0;S<Je.length;S++)a[Je[S]]=v[S].offsetWidth,l[Je[S]]=v[S].offsetHeight;return[2,Ir.filter(function(m){return f(y[m])})]})})})}function ku(){var e=navigator.plugins;if(e){for(var t=[],n=0;n<e.length;++n){var r=e[n];if(r){for(var o=[],i=0;i<r.length;++i){var a=r[i];o.push({type:a.type,suffixes:a.suffixes})}t.push({name:r.name,description:r.description,mimeTypes:o})}}return t}}function $u(){return Ou(Vu())}function Ou(e){var t,n=!1,r,o,i=Lu(),a=i[0],l=i[1];return ju(a,l)?(n=Fu(l),e?r=o="skipped":(t=Pu(a,l),r=t[0],o=t[1])):r=o="unsupported",{winding:n,geometry:r,text:o}}function Lu(){var e=document.createElement("canvas");return e.width=1,e.height=1,[e,e.getContext("2d")]}function ju(e,t){return!!(t&&e.toDataURL)}function Fu(e){return e.rect(0,0,10,10),e.rect(2,2,6,6),!e.isPointInPath(5,5,"evenodd")}function Pu(e,t){xu(e,t);var n=hn(e),r=hn(e);if(n!==r)return["unstable","unstable"];Mu(e,t);var o=hn(e);return[o,n]}function xu(e,t){e.width=240,e.height=60,t.textBaseline="alphabetic",t.fillStyle="#f60",t.fillRect(100,1,62,20),t.fillStyle="#069",t.font='11pt "Times New Roman"';var n="Cwm fjordbank gly ".concat("😃");t.fillText(n,2,15),t.fillStyle="rgba(102, 204, 0, 0.2)",t.font="18pt Arial",t.fillText(n,4,45)}function Mu(e,t){e.width=122,e.height=110,t.globalCompositeOperation="multiply";for(var n=0,r=[["#f2f",40,40],["#2ff",80,40],["#ff2",60,80]];n<r.length;n++){var o=r[n],i=o[0],a=o[1],l=o[2];t.fillStyle=i,t.beginPath(),t.arc(a,l,40,0,Math.PI*2,!0),t.closePath(),t.fill()}t.fillStyle="#f9c",t.arc(60,60,60,0,Math.PI*2,!0),t.arc(60,60,20,0,Math.PI*2,!0),t.fill("evenodd")}function hn(e){return e.toDataURL()}function Vu(){return Oe()&&gt()&&mt()}function Au(){var e=navigator,t=0,n;e.maxTouchPoints!==void 0?t=Dn(e.maxTouchPoints):e.msMaxTouchPoints!==void 0&&(t=e.msMaxTouchPoints);try{document.createEvent("TouchEvent"),n=!0}catch{n=!1}var r="ontouchstart"in window;return{maxTouchPoints:t,touchEvent:n,touchStart:r}}function Tu(){return navigator.oscpu}function Bu(){var e=navigator,t=[],n=e.language||e.userLanguage||e.browserLanguage||e.systemLanguage;if(n!==void 0&&t.push([n]),Array.isArray(e.languages))pt()&&au()||t.push(e.languages);else if(typeof e.languages=="string"){var r=e.languages;r&&t.push(r.split(","))}return t}function Iu(){return window.screen.colorDepth}function zu(){return xe(Ce(navigator.deviceMemory),void 0)}function Nu(){if(!(Oe()&&gt()&&mt()))return Du()}function Du(){var e=screen,t=function(r){return xe(Dn(r),null)},n=[t(e.width),t(e.height)];return n.sort().reverse(),n}var Wu=2500,Ru=10,$t,pn;function Hu(){if(pn===void 0){var e=function(){var t=Ln();jn(t)?pn=setTimeout(e,Wu):($t=t,pn=void 0)};e()}}function Gu(){var e=this;return Hu(),function(){return je(e,void 0,void 0,function(){var t;return Fe(this,function(n){switch(n.label){case 0:return t=Ln(),jn(t)?$t?[2,wo([],$t,!0)]:cu()?[4,du()]:[3,2]:[3,2];case 1:n.sent(),t=Ln(),n.label=2;case 2:return jn(t)||($t=t),[2,t]}})})}}function Zu(){var e=this;if(Oe()&&gt()&&mt())return function(){return Promise.resolve(void 0)};var t=Gu();return function(){return je(e,void 0,void 0,function(){var n,r;return Fe(this,function(o){switch(o.label){case 0:return[4,t()];case 1:return n=o.sent(),r=function(i){return i===null?null:Eo(i,Ru)},[2,[r(n[0]),r(n[1]),r(n[2]),r(n[3])]]}})})}}function Ln(){var e=screen;return[xe(Ce(e.availTop),null),xe(Ce(e.width)-Ce(e.availWidth)-xe(Ce(e.availLeft),0),null),xe(Ce(e.height)-Ce(e.availHeight)-xe(Ce(e.availTop),0),null),xe(Ce(e.availLeft),null)]}function jn(e){for(var t=0;t<4;++t)if(e[t])return!1;return!0}function Yu(){return xe(Dn(navigator.hardwareConcurrency),void 0)}function Xu(){var e,t=(e=window.Intl)===null||e===void 0?void 0:e.DateTimeFormat;if(t){var n=new t().resolvedOptions().timeZone;if(n)return n}var r=-qu();return"UTC".concat(r>=0?"+":"").concat(r)}function qu(){var e=new Date().getFullYear();return Math.max(Ce(new Date(e,0,1).getTimezoneOffset()),Ce(new Date(e,6,1).getTimezoneOffset()))}function Ju(){try{return!!window.sessionStorage}catch{return!0}}function Uu(){try{return!!window.localStorage}catch{return!0}}function Ku(){if(!(_o()||iu()))try{return!!window.indexedDB}catch{return!0}}function Qu(){return!!window.openDatabase}function ec(){return navigator.cpuClass}function tc(){var e=navigator.platform;return e==="MacIntel"&&Oe()&&!Wn()?uu()?"iPad":"iPhone":e}function nc(){return navigator.vendor||""}function rc(){for(var e=[],t=0,n=["chrome","safari","__crWeb","__gCrWeb","yandex","__yb","__ybro","__firefox__","__edgeTrackingPreventionStatistics","webkit","oprt","samsungAr","ucweb","UCShellJava","puffinDevice"];t<n.length;t++){var r=n[t],o=window[r];o&&typeof o=="object"&&e.push(r)}return e.sort()}function oc(){var e=document;try{e.cookie="cookietest=1; SameSite=Strict;";var t=e.cookie.indexOf("cookietest=")!==-1;return e.cookie="cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT",t}catch{return!1}}function ic(){var e=atob;return{abpIndo:["#Iklan-Melayang","#Kolom-Iklan-728","#SidebarIklan-wrapper",'[title="ALIENBOLA" i]',e("I0JveC1CYW5uZXItYWRz")],abpvn:[".quangcao","#mobileCatfish",e("LmNsb3NlLWFkcw=="),'[id^="bn_bottom_fixed_"]',"#pmadv"],adBlockFinland:[".mainostila",e("LnNwb25zb3JpdA=="),".ylamainos",e("YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd")],adBlockPersian:["#navbar_notice_50",".kadr",'TABLE[width="140px"]',"#divAgahi",e("YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd")],adBlockWarningRemoval:["#adblock-honeypot",".adblocker-root",".wp_adblock_detect",e("LmhlYWRlci1ibG9ja2VkLWFk"),e("I2FkX2Jsb2NrZXI=")],adGuardAnnoyances:[".hs-sosyal","#cookieconsentdiv",'div[class^="app_gdpr"]',".as-oil",'[data-cypress="soft-push-notification-modal"]'],adGuardBase:[".BetterJsPopOverlay",e("I2FkXzMwMFgyNTA="),e("I2Jhbm5lcmZsb2F0MjI="),e("I2NhbXBhaWduLWJhbm5lcg=="),e("I0FkLUNvbnRlbnQ=")],adGuardChinese:[e("LlppX2FkX2FfSA=="),e("YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd"),"#widget-quan",e("YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd"),e("YVtocmVmKj0iLjE5NTZobC5jb20vIl0=")],adGuardFrench:["#pavePub",e("LmFkLWRlc2t0b3AtcmVjdGFuZ2xl"),".mobile_adhesion",".widgetadv",e("LmFkc19iYW4=")],adGuardGerman:['aside[data-portal-id="leaderboard"]'],adGuardJapanese:["#kauli_yad_1",e("YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0="),e("Ll9wb3BJbl9pbmZpbml0ZV9hZA=="),e("LmFkZ29vZ2xl"),e("Ll9faXNib29zdFJldHVybkFk")],adGuardMobile:[e("YW1wLWF1dG8tYWRz"),e("LmFtcF9hZA=="),'amp-embed[type="24smi"]',"#mgid_iframe1",e("I2FkX2ludmlld19hcmVh")],adGuardRussian:[e("YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0="),e("LnJlY2xhbWE="),'div[id^="smi2adblock"]',e("ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd"),"#psyduckpockeball"],adGuardSocial:[e("YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0="),e("YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0="),".etsy-tweet","#inlineShare",".popup-social"],adGuardSpanishPortuguese:["#barraPublicidade","#Publicidade","#publiEspecial","#queTooltip",".cnt-publi"],adGuardTrackingProtection:["#qoo-counter",e("YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=="),e("YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0="),e("YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=="),"#top100counter"],adGuardTurkish:["#backkapat",e("I3Jla2xhbWk="),e("YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0="),e("YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd"),e("YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ==")],bulgarian:[e("dGQjZnJlZW5ldF90YWJsZV9hZHM="),"#ea_intext_div",".lapni-pop-over","#xenium_hot_offers"],easyList:[".yb-floorad",e("LndpZGdldF9wb19hZHNfd2lkZ2V0"),e("LnRyYWZmaWNqdW5reS1hZA=="),".textad_headline",e("LnNwb25zb3JlZC10ZXh0LWxpbmtz")],easyListChina:[e("LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=="),e("LmZyb250cGFnZUFkdk0="),"#taotaole","#aafoot.top_box",".cfa_popup"],easyListCookie:[".ezmob-footer",".cc-CookieWarning","[data-cookie-number]",e("LmF3LWNvb2tpZS1iYW5uZXI="),".sygnal24-gdpr-modal-wrap"],easyListCzechSlovak:["#onlajny-stickers",e("I3Jla2xhbW5pLWJveA=="),e("LnJla2xhbWEtbWVnYWJvYXJk"),".sklik",e("W2lkXj0ic2tsaWtSZWtsYW1hIl0=")],easyListDutch:[e("I2FkdmVydGVudGll"),e("I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=="),".adstekst",e("YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0="),"#semilo-lrectangle"],easyListGermany:["#SSpotIMPopSlider",e("LnNwb25zb3JsaW5rZ3J1ZW4="),e("I3dlcmJ1bmdza3k="),e("I3Jla2xhbWUtcmVjaHRzLW1pdHRl"),e("YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0=")],easyListItaly:[e("LmJveF9hZHZfYW5udW5jaQ=="),".sb-box-pubbliredazionale",e("YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ==")],easyListLithuania:[e("LnJla2xhbW9zX3RhcnBhcw=="),e("LnJla2xhbW9zX251b3JvZG9z"),e("aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd"),e("aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd"),e("aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd")],estonian:[e("QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==")],fanboyAnnoyances:["#ac-lre-player",".navigate-to-top","#subscribe_popup",".newsletter_holder","#back-top"],fanboyAntiFacebook:[".util-bar-module-firefly-visible"],fanboyEnhancedTrackers:[".open.pushModal","#issuem-leaky-paywall-articles-zero-remaining-nag","#sovrn_container",'div[class$="-hide"][zoompage-fontsize][style="display: block;"]',".BlockNag__Card"],fanboySocial:["#FollowUs","#meteored_share","#social_follow",".article-sharer",".community__social-desc"],frellwitSwedish:[e("YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=="),e("YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=="),"article.category-samarbete",e("ZGl2LmhvbGlkQWRz"),"ul.adsmodern"],greekAdBlock:[e("QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd"),e("QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=="),e("QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd"),"DIV.agores300","TABLE.advright"],hungarian:["#cemp_doboz",".optimonk-iframe-container",e("LmFkX19tYWlu"),e("W2NsYXNzKj0iR29vZ2xlQWRzIl0="),"#hirdetesek_box"],iDontCareAboutCookies:['.alert-info[data-block-track*="CookieNotice"]',".ModuleTemplateCookieIndicator",".o--cookies--container","#cookies-policy-sticky","#stickyCookieBar"],icelandicAbp:[e("QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==")],latvian:[e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0OiA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0="),e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6IDMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ==")],listKr:[e("YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0="),e("I2xpdmVyZUFkV3JhcHBlcg=="),e("YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=="),e("aW5zLmZhc3R2aWV3LWFk"),".revenue_unit_item.dable"],listeAr:[e("LmdlbWluaUxCMUFk"),".right-and-left-sponsers",e("YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=="),e("YVtocmVmKj0iYm9vcmFxLm9yZyJd"),e("YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd")],listeFr:[e("YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=="),e("I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=="),e("YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0="),".site-pub-interstitiel",'div[id^="crt-"][data-criteo-id]'],officialPolish:["#ceneo-placeholder-ceneo-12",e("W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd"),e("YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=="),e("YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=="),e("ZGl2I3NrYXBpZWNfYWQ=")],ro:[e("YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0="),e("YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd"),'a[href^="/url/"]'],ruAd:[e("YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd"),e("YVtocmVmKj0iLy91dGltZy5ydS8iXQ=="),e("YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0="),"#pgeldiz",".yandex-rtb-block"],thaiAds:["a[href*=macau-uta-popup]",e("I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=="),e("LmFkczMwMHM="),".bumq",".img-kosana"],webAnnoyancesUltralist:["#mod-social-share-2","#social-tools",e("LmN0cGwtZnVsbGJhbm5lcg=="),".zergnet-recommend",".yt.btn-link.btn-md.btn"]}}function ac(e){var t=e===void 0?{}:e,n=t.debug;return je(this,void 0,void 0,function(){var r,o,i,a,l,s;return Fe(this,function(u){switch(u.label){case 0:return lc()?(r=ic(),o=Object.keys(r),i=(s=[]).concat.apply(s,o.map(function(d){return r[d]})),[4,sc(i)]):[2,void 0];case 1:return a=u.sent(),n&&uc(r,a),l=o.filter(function(d){var c=r[d],f=fe(c.map(function(v){return a[v]}));return f>c.length*.6}),l.sort(),[2,l]}})})}function lc(){return Oe()||Rn()}function sc(e){var t;return je(this,void 0,void 0,function(){var n,r,o,i,s,a,l,s;return Fe(this,function(u){switch(u.label){case 0:for(n=document,r=n.createElement("div"),o=new Array(e.length),i={},zr(r),s=0;s<e.length;++s)a=yu(e[s]),a.tagName==="DIALOG"&&a.show(),l=n.createElement("div"),zr(l),l.appendChild(a),r.appendChild(l),o[s]=a;u.label=1;case 1:return n.body?[3,3]:[4,xt(50)];case 2:return u.sent(),[3,1];case 3:n.body.appendChild(r);try{for(s=0;s<e.length;++s)o[s].offsetParent||(i[e[s]]=!0)}finally{(t=r.parentNode)===null||t===void 0||t.removeChild(r)}return[2,i]}})})}function zr(e){e.style.setProperty("visibility","hidden","important"),e.style.setProperty("display","block","important")}function uc(e,t){for(var n="DOM blockers debug:\n```",r=0,o=Object.keys(e);r<o.length;r++){var i=o[r];n+=`
`.concat(i,":");for(var a=0,l=e[i];a<l.length;a++){var s=l[a];n+=`
  `.concat(t[s]?"🚫":"➡️"," ").concat(s)}}console.log("".concat(n,"\n```"))}function cc(){for(var e=0,t=["rec2020","p3","srgb"];e<t.length;e++){var n=t[e];if(matchMedia("(color-gamut: ".concat(n,")")).matches)return n}}function dc(){if(Nr("inverted"))return!0;if(Nr("none"))return!1}function Nr(e){return matchMedia("(inverted-colors: ".concat(e,")")).matches}function fc(){if(Dr("active"))return!0;if(Dr("none"))return!1}function Dr(e){return matchMedia("(forced-colors: ".concat(e,")")).matches}var vc=100;function hc(){if(matchMedia("(min-monochrome: 0)").matches){for(var e=0;e<=vc;++e)if(matchMedia("(max-monochrome: ".concat(e,")")).matches)return e;throw new Error("Too high value")}}function pc(){if(Ue("no-preference"))return 0;if(Ue("high")||Ue("more"))return 1;if(Ue("low")||Ue("less"))return-1;if(Ue("forced"))return 10}function Ue(e){return matchMedia("(prefers-contrast: ".concat(e,")")).matches}function mc(){if(Wr("reduce"))return!0;if(Wr("no-preference"))return!1}function Wr(e){return matchMedia("(prefers-reduced-motion: ".concat(e,")")).matches}function gc(){if(Rr("reduce"))return!0;if(Rr("no-preference"))return!1}function Rr(e){return matchMedia("(prefers-reduced-transparency: ".concat(e,")")).matches}function bc(){if(Hr("high"))return!0;if(Hr("standard"))return!1}function Hr(e){return matchMedia("(dynamic-range: ".concat(e,")")).matches}var I=Math,ce=function(){return 0};function yc(){var e=I.acos||ce,t=I.acosh||ce,n=I.asin||ce,r=I.asinh||ce,o=I.atanh||ce,i=I.atan||ce,a=I.sin||ce,l=I.sinh||ce,s=I.cos||ce,u=I.cosh||ce,d=I.tan||ce,c=I.tanh||ce,f=I.exp||ce,v=I.expm1||ce,y=I.log1p||ce,S=function(C){return I.pow(I.PI,C)},_=function(C){return I.log(C+I.sqrt(C*C-1))},m=function(C){return I.log(C+I.sqrt(C*C+1))},w=function(C){return I.log((1+C)/(1-C))/2},k=function(C){return I.exp(C)-1/I.exp(C)/2},x=function(C){return(I.exp(C)+1/I.exp(C))/2},A=function(C){return I.exp(C)-1},H=function(C){return(I.exp(2*C)-1)/(I.exp(2*C)+1)},$=function(C){return I.log(1+C)};return{acos:e(.12312423423423424),acosh:t(1e308),acoshPf:_(1e154),asin:n(.12312423423423424),asinh:r(1),asinhPf:m(1),atanh:o(.5),atanhPf:w(.5),atan:i(.5),sin:a(-1e300),sinh:l(1),sinhPf:k(1),cos:s(10.000000000123),cosh:u(1),coshPf:x(1),tan:d(-1e300),tanh:c(1),tanhPf:H(1),exp:f(1),expm1:v(1),expm1Pf:A(1),log1p:y(10),log1pPf:$(10),powPI:S(-100)}}var wc="mmMwWLliI0fiflO&1",mn={default:[],apple:[{font:"-apple-system-body"}],serif:[{fontFamily:"serif"}],sans:[{fontFamily:"sans-serif"}],mono:[{fontFamily:"monospace"}],min:[{fontSize:"1px"}],system:[{fontFamily:"system-ui"}]};function Cc(){return Sc(function(e,t){for(var n={},r={},o=0,i=Object.keys(mn);o<i.length;o++){var a=i[o],l=mn[a],s=l[0],u=s===void 0?{}:s,d=l[1],c=d===void 0?wc:d,f=e.createElement("span");f.textContent=c,f.style.whiteSpace="nowrap";for(var v=0,y=Object.keys(u);v<y.length;v++){var S=y[v],_=u[S];_!==void 0&&(f.style[S]=_)}n[a]=f,t.append(e.createElement("br"),f)}for(var m=0,w=Object.keys(mn);m<w.length;m++){var a=w[m];r[a]=n[a].getBoundingClientRect().width}return r})}function Sc(e,t){return t===void 0&&(t=4e3),$o(function(n,r){var o=r.document,i=o.body,a=i.style;a.width="".concat(t,"px"),a.webkitTextSizeAdjust=a.textSizeAdjust="none",pt()?i.style.zoom="".concat(1/r.devicePixelRatio):Oe()&&(i.style.zoom="reset");var l=o.createElement("div");return l.textContent=wo([],Array(t/20<<0),!0).map(function(){return"word"}).join(" "),i.appendChild(l),e(o,i)},'<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1">')}function Ec(){return navigator.pdfViewerEnabled}function _c(){var e=new Float32Array(1),t=new Uint8Array(e.buffer);return e[0]=1/0,e[0]=e[0]-e[0],t[3]}function kc(){var e=window.ApplePaySession;if(typeof(e==null?void 0:e.canMakePayments)!="function")return-1;if($c())return-3;try{return e.canMakePayments()?1:0}catch(t){return Oc(t)}}var $c=Cu;function Oc(e){if(e instanceof Error&&e.name==="InvalidAccessError"&&/\bfrom\b.*\binsecure\b/i.test(e.message))return-2;throw e}function Lc(){var e,t=document.createElement("a"),n=(e=t.attributionSourceId)!==null&&e!==void 0?e:t.attributionsourceid;return n===void 0?void 0:String(n)}var Oo=-1,Lo=-2,jc=new Set([10752,2849,2884,2885,2886,2928,2929,2930,2931,2932,2960,2961,2962,2963,2964,2965,2966,2967,2968,2978,3024,3042,3088,3089,3106,3107,32773,32777,32777,32823,32824,32936,32937,32938,32939,32968,32969,32970,32971,3317,33170,3333,3379,3386,33901,33902,34016,34024,34076,3408,3410,3411,3412,3413,3414,3415,34467,34816,34817,34818,34819,34877,34921,34930,35660,35661,35724,35738,35739,36003,36004,36005,36347,36348,36349,37440,37441,37443,7936,7937,7938]),Fc=new Set([34047,35723,36063,34852,34853,34854,34229,36392,36795,38449]),Pc=["FRAGMENT_SHADER","VERTEX_SHADER"],xc=["LOW_FLOAT","MEDIUM_FLOAT","HIGH_FLOAT","LOW_INT","MEDIUM_INT","HIGH_INT"],jo="WEBGL_debug_renderer_info",Mc="WEBGL_polygon_mode";function Vc(e){var t,n,r,o,i,a,l=e.cache,s=Fo(l);if(!s)return Oo;if(!xo(s))return Lo;var u=Po()?null:s.getExtension(jo);return{version:((t=s.getParameter(s.VERSION))===null||t===void 0?void 0:t.toString())||"",vendor:((n=s.getParameter(s.VENDOR))===null||n===void 0?void 0:n.toString())||"",vendorUnmasked:u?(r=s.getParameter(u.UNMASKED_VENDOR_WEBGL))===null||r===void 0?void 0:r.toString():"",renderer:((o=s.getParameter(s.RENDERER))===null||o===void 0?void 0:o.toString())||"",rendererUnmasked:u?(i=s.getParameter(u.UNMASKED_RENDERER_WEBGL))===null||i===void 0?void 0:i.toString():"",shadingLanguageVersion:((a=s.getParameter(s.SHADING_LANGUAGE_VERSION))===null||a===void 0?void 0:a.toString())||""}}function Ac(e){var t=e.cache,n=Fo(t);if(!n)return Oo;if(!xo(n))return Lo;var r=n.getSupportedExtensions(),o=n.getContextAttributes(),i=[],a=[],l=[],s=[],u=[];if(o)for(var d=0,c=Object.keys(o);d<c.length;d++){var f=c[d];a.push("".concat(f,"=").concat(o[f]))}for(var v=Gr(n),y=0,S=v;y<S.length;y++){var _=S[y],m=n[_];l.push("".concat(_,"=").concat(m).concat(jc.has(m)?"=".concat(n.getParameter(m)):""))}if(r)for(var w=0,k=r;w<k.length;w++){var x=k[w];if(!(x===jo&&Po()||x===Mc&&Ic())){var A=n.getExtension(x);if(!A){i.push(x);continue}for(var H=0,$=Gr(A);H<$.length;H++){var _=$[H],m=A[_];s.push("".concat(_,"=").concat(m).concat(Fc.has(m)?"=".concat(n.getParameter(m)):""))}}}for(var C=0,D=Pc;C<D.length;C++)for(var z=D[C],te=0,q=xc;te<q.length;te++){var O=q[te],se=Tc(n,z,O);u.push("".concat(z,".").concat(O,"=").concat(se.join(",")))}return s.sort(),l.sort(),{contextAttributes:a,parameters:l,shaderPrecisions:u,extensions:r,extensionParameters:s,unsupportedExtensions:i}}function Fo(e){if(e.webgl)return e.webgl.context;var t=document.createElement("canvas"),n;t.addEventListener("webglCreateContextError",function(){return n=void 0});for(var r=0,o=["webgl","experimental-webgl"];r<o.length;r++){var i=o[r];try{n=t.getContext(i)}catch{}if(n)break}return e.webgl={context:n},n}function Tc(e,t,n){var r=e.getShaderPrecisionFormat(e[t],e[n]);return r?[r.rangeMin,r.rangeMax,r.precision]:[]}function Gr(e){var t=Object.keys(e.__proto__);return t.filter(Bc)}function Bc(e){return typeof e=="string"&&!e.match(/[^A-Z0-9_x]/)}function Po(){return ko()}function Ic(){return pt()||Oe()}function xo(e){return typeof e.getParameter=="function"}function zc(){var e=Rn()||Oe();if(!e)return-2;if(!window.AudioContext)return-1;var t=new AudioContext().baseLatency;return t==null?-1:isFinite(t)?t:-3}function Nc(){if(!window.Intl)return-1;var e=window.Intl.DateTimeFormat;if(!e)return-2;var t=e().resolvedOptions().locale;return!t&&t!==""?-3:t}var Dc={fonts:_u,domBlockers:ac,fontPreferences:Cc,audio:vu,screenFrame:Zu,canvas:$u,osCpu:Tu,languages:Bu,colorDepth:Iu,deviceMemory:zu,screenResolution:Nu,hardwareConcurrency:Yu,timezone:Xu,sessionStorage:Ju,localStorage:Uu,indexedDB:Ku,openDatabase:Qu,cpuClass:ec,platform:tc,plugins:ku,touchSupport:Au,vendor:nc,vendorFlavors:rc,cookiesEnabled:oc,colorGamut:cc,invertedColors:dc,forcedColors:fc,monochrome:hc,contrast:pc,reducedMotion:mc,reducedTransparency:gc,hdr:bc,math:yc,pdfViewerEnabled:Ec,architecture:_c,applePay:kc,privateClickMeasurement:Lc,audioBaseLatency:zc,dateTimeLocale:Nc,webGlBasics:Vc,webGlExtensions:Ac};function Wc(e){return ou(Dc,e,[])}var Rc="$ if upgrade to Pro: https://fpjs.dev/pro";function Hc(e){var t=Gc(e),n=Zc(t);return{score:t,comment:Rc.replace(/\$/g,"".concat(n))}}function Gc(e){if(Rn())return .4;if(Oe())return Wn()&&!(gt()&&mt())?.5:.3;var t="value"in e.platform?e.platform.value:"";return/^Win/.test(t)?.6:/^Mac/.test(t)?.5:.7}function Zc(e){return Eo(.99+.01*e,1e-4)}function Yc(e){for(var t="",n=0,r=Object.keys(e).sort();n<r.length;n++){var o=r[n],i=e[o],a="error"in i?"error":JSON.stringify(i.value);t+="".concat(t?"|":"").concat(o.replace(/([:|\\])/g,"\\$1"),":").concat(a)}return t}function Mo(e){return JSON.stringify(e,function(t,n){return n instanceof Error?eu(n):n},2)}function Vo(e){return Qs(Yc(e))}function Xc(e){var t,n=Hc(e);return{get visitorId(){return t===void 0&&(t=Vo(this.components)),t},set visitorId(r){t=r},confidence:n,components:e,version:Co}}function qc(e){return e===void 0&&(e=50),Hs(e,e*2)}function Jc(e,t){var n=Date.now();return{get:function(r){return je(this,void 0,void 0,function(){var o,i,a;return Fe(this,function(l){switch(l.label){case 0:return o=Date.now(),[4,e()];case 1:return i=l.sent(),a=Xc(i),(t||r!=null&&r.debug)&&console.log("Copy the text below to get the debug data:\n\n```\nversion: ".concat(a.version,`
userAgent: `).concat(navigator.userAgent,`
timeBetweenLoadAndGet: `).concat(o-n,`
visitorId: `).concat(a.visitorId,`
components: `).concat(Mo(i),"\n```")),[2,a]}})})}}}function Uc(){if(!(window.__fpjs_d_m||Math.random()>=.001))try{var e=new XMLHttpRequest;e.open("get","https://m1.openfpcdn.io/fingerprintjs/v".concat(Co,"/npm-monitoring"),!0),e.send()}catch(t){console.error(t)}}function Kc(e){var t;return e===void 0&&(e={}),je(this,void 0,void 0,function(){var n,r,o;return Fe(this,function(i){switch(i.label){case 0:return(!((t=e.monitoring)!==null&&t!==void 0)||t)&&Uc(),n=e.delayFallback,r=e.debug,[4,qc(n)];case 1:return i.sent(),o=Wc({cache:{},debug:r}),[2,Jc(o,r)]}})})}var Qc={load:Kc,hashComponents:Vo,componentsToDebugString:Mo},ed={BASE_API_URL:"https://flowmix.turntip.cn/api/v1",BASE_WS_URL:"wss://flowmix.turntip.cn/ws",UPLOAD_PATH:"/upload/free"};function fd(){const e=["#FF5252","#FF9800","#FFEB3B","#4CAF50","#2196F3","#9C27B0","#00BCD4","#795548"],t=Math.floor(Math.random()*e.length);return e[t]}const vd=ed.BASE_WS_URL,hd=async()=>(await Qc.load().then(n=>n.get())).visitorId;export{od as A,Fa as B,hl as C,lo as D,uo as E,pa as F,Xo as G,rr as H,Tn as I,so as J,Ri as K,qi as L,ta as M,ut as N,ud as O,Qn as P,id as Q,En as R,cd as S,_l as T,vn as U,dd as V,vd as W,Cs as X,hd as Y,fd as Z,K as _,Qr as a,X as b,de as c,Mt as d,vt as e,Kn as f,Rt as g,Be as h,rd as i,lt as j,Ht as k,en as l,An as m,Bi as n,Gt as o,Bt as p,sd as q,ld as r,ht as s,pl as t,Qo as u,fl as v,tt as w,nd as x,qo as y,ad as z};
