import { Node, mergeAttributes } from "@tiptap/core";
import { prefixClass } from "../../utils/prefix.js";

export default Node.create({
  name: "chart",

  onCreate() {
    console.log('Chart extension created');
  },

  addOptions() {
    return {
      ...this.parent?.(),
      name: "chart",
      desc: "图表",
      slash: true,
      command: ({ editor, range }) => {
        console.log('Chart extension slash command called');

        // 删除斜杠命令的文本
        if (range) {
          editor.chain().focus().deleteRange(range).run();
        }

        // 调用全局的图表编辑器打开函数
        if (window.openChartEditor) {
          console.log('Opening chart editor from slash command');
          // 设置标记表示这是从斜杠命令触发的
          window.isSlashCommand = true;
          window.slashEditor = editor;
          window.openChartEditor();
        } else {
          console.warn('Chart editor not available, falling back to direct insertion');
          // 如果图表编辑器不可用，回退到直接插入
          const chartData = {
            type: "bar",
            title: "新建图表",
            data: {
              categories: ["类别1", "类别2", "类别3", "类别4"],
              series: [
                {
                  name: "系列1",
                  data: [4.3, 2.5, 4.5, 5.0]
                }
              ]
            },
            config: {
              theme: "default",
              showLegend: true,
              legendPosition: "bottom",
              showGrid: true,
              showTooltip: true
            }
          };

          editor
            .chain()
            .focus()
            .insertContent({
              type: "chart",
              attrs: {
                data: JSON.stringify(chartData)
              }
            })
            .run();
        }
      },
      isActive: ({ editor }) => editor.isActive("chart"),
      isDisabled: ({ editor }) => !editor.can().insertContent({ type: "chart" }),
      shortcutkeys: "Mod-Shift-C",
      HTMLAttributes: {
        class: `${prefixClass}__chart`,
      },
    };
  },

  group: "block",

  content: "",

  atom: true,

  addAttributes() {
    return {
      data: {
        default: null,
        parseHTML: element => element.getAttribute('data-chart'),
        renderHTML: attributes => {
          if (!attributes.data) {
            return {};
          }
          return {
            'data-chart': attributes.data,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-chart]',
      },
    ];
  },

  renderHTML({ node, HTMLAttributes }) {
    const attrs = mergeAttributes(this.options.HTMLAttributes, HTMLAttributes);

    // 确保data-chart属性被正确设置
    if (node.attrs.data) {
      attrs['data-chart'] = node.attrs.data;
    }

    return ['div', attrs];
  },

  addCommands() {
    return {
      setChart: (options) => ({ commands }) => {
        console.log('setChart command called with options:', options);
        const result = commands.insertContent({
          type: this.name,
          attrs: options,
        });
        console.log('setChart command result:', result);
        return result;
      },
      insertChart: (options) => ({ commands }) => {
        console.log('insertChart command called with options:', options);
        const result = commands.insertContent({
          type: this.name,
          attrs: options,
        });
        console.log('insertChart command result:', result);
        return result;
      },
      updateChart: (attributes) => ({ commands }) => {
        console.log('updateChart command called with attributes:', attributes);
        return commands.updateAttributes(this.name, attributes);
      },
    };
  },

  addNodeView() {
    const chartExtension = this;

    return ({ node, HTMLAttributes, getPos, editor }) => {
      console.log('Creating NodeView for chart with data:', node.attrs.data);

      const dom = document.createElement('div');
      dom.className = `${prefixClass}__chart-container`;

      // 创建图表容器
      const chartContainer = document.createElement('div');
      chartContainer.className = `${prefixClass}__chart-content`;
      chartContainer.style.cssText = `
        width: 100%;
        height: 400px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        position: relative;
        background: #fff;
        overflow: hidden;
      `;

      // 添加防止白色蒙层的全局样式
      if (!document.getElementById('chart-overlay-fix')) {
        const style = document.createElement('style');
        style.id = 'chart-overlay-fix';
        style.textContent = `
          .${prefixClass}__chart-content canvas {
            pointer-events: auto !important;
          }
          .${prefixClass}__chart-content::before,
          .${prefixClass}__chart-content::after {
            display: none !important;
          }
          .${prefixClass}__chart-content > div {
            background: transparent !important;
          }
          .${prefixClass}__chart-content .echarts-tooltip {
            pointer-events: none !important;
            z-index: 9999 !important;
          }
          .${prefixClass}__chart-content:hover::before {
            display: none !important;
          }
          .${prefixClass}__chart-content * {
            pointer-events: auto !important;
          }
        `;
        document.head.appendChild(style);
      }

      // 创建编辑按钮
      const editButton = document.createElement('button');
      editButton.className = `${prefixClass}__chart-edit-btn`;
      editButton.innerHTML = '编辑图表';
      editButton.style.cssText = `
        position: absolute;
        top: 8px;
        right: 8px;
        padding: 4px 8px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        z-index: 10;
      `;

      // 编辑按钮点击事件
      editButton.addEventListener('click', () => {
        const chartData = node.attrs.data ? JSON.parse(node.attrs.data) : null;
        // 设置当前编辑的图表位置
        window.currentChartPosition = getPos();
        // 触发编辑弹窗
        if (window.openChartEditor) {
          window.openChartEditor(chartData);
        }
      });

      dom.appendChild(chartContainer);
      dom.appendChild(editButton);

      // 渲染图表的函数 - 内联实现
      const renderChart = async (container, dataString) => {
        console.log('NodeView renderChart called with:', dataString);

        // 清理旧的图表实例和事件监听器
        if (container._chartInstance) {
          container._chartInstance.dispose();
          container._chartInstance = null;
        }

        // 清理resize处理器
        if (container._resizeHandler) {
          window.removeEventListener('resize', container._resizeHandler);
          container._resizeHandler = null;
        }

        if (!dataString) {
          container.innerHTML = '<div style="padding: 20px; text-align: center; color: #999;">点击编辑按钮配置图表</div>';
          return;
        }

        try {
          const chartData = JSON.parse(dataString);
          console.log('Parsed chart data:', chartData);

          // 验证数据结构
          if (!chartData.type || !chartData.data || !chartData.data.categories || !chartData.data.series) {
            throw new Error('Invalid chart data structure');
          }

          // 清空容器
          container.innerHTML = '';

          // 确保容器有正确的尺寸和样式
          container.style.width = '100%';
          container.style.height = '400px';
          container.style.minHeight = '400px';
          container.style.position = 'relative';
          container.style.display = 'block';

          // 获取或加载 ECharts
          let echarts;
          if (window.echarts) {
            echarts = window.echarts;
          } else {
            const echartsModule = await import('echarts');
            echarts = echartsModule.default || echartsModule;
            window.echarts = echarts;
          }

          // 等待DOM更新后再初始化图表
          await new Promise(resolve => requestAnimationFrame(resolve));

          // 再次检查容器尺寸
          const rect = container.getBoundingClientRect();
          if (rect.width === 0 || rect.height === 0) {
            console.warn('Container size is 0, retrying...');
            setTimeout(() => renderChart(container, dataString), 100);
            return;
          }

          // 创建图表实例
          const chart = echarts.init(container, null, {
            renderer: 'canvas',
            useDirtyRect: false
          });
          container._chartInstance = chart;

          // 生成 ECharts 配置
          const option = generateEChartsOption(chartData);

          // 设置图表配置
          chart.setOption(option, true);

          // 多次resize确保正确显示
          setTimeout(() => {
            if (chart && !chart.isDisposed()) {
              chart.resize();
            }
          }, 50);

          setTimeout(() => {
            if (chart && !chart.isDisposed()) {
              chart.resize();
            }
          }, 200);

          // 监听窗口大小变化
          const resizeHandler = () => {
            if (chart && !chart.isDisposed()) {
              chart.resize();
            }
          };

          window.addEventListener('resize', resizeHandler);

          // 保存resize处理器以便清理
          container._resizeHandler = resizeHandler;

          console.log('Chart rendered successfully');

        } catch (error) {
          console.error('Chart rendering error:', error);
          container.innerHTML = `<div style="padding: 20px; text-align: center; color: #ff4d4f;">图表渲染错误: ${error.message}</div>`;
        }
      };

      // ECharts配置生成函数
      const generateEChartsOption = (chartData) => {
        const { type, title, data, config } = chartData;

        const baseOption = {
          title: {
            text: title || '图表标题',
            left: 'center',
            top: 20,
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold'
            }
          },
          tooltip: config.showTooltip !== false ? {
            trigger: type === 'pie' ? 'item' : 'axis',
            backgroundColor: 'rgba(50,50,50,0.95)',
            borderColor: '#333',
            borderWidth: 1,
            textStyle: {
              color: '#fff',
              fontSize: 12
            },
            confine: true,
            appendToBody: true,
            enterable: false,
            hideDelay: 0,
            showDelay: 0,
            transitionDuration: 0,
            extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.15); border-radius: 4px; z-index: 9999 !important; pointer-events: none !important;',
            axisPointer: {
              type: type === 'line' || type === 'area' ? 'cross' : 'shadow'
            }
          } : undefined,
          legend: config.showLegend !== false ? {
            orient: config.legendPosition === 'left' || config.legendPosition === 'right' ? 'vertical' : 'horizontal',
            left: config.legendPosition === 'left' ? 0 :
                  config.legendPosition === 'right' ? 'right' : 'center',
            right: config.legendPosition === 'right' ? 0 : undefined,
            bottom: config.legendPosition === 'bottom' ? 0 : undefined,
            top: config.legendPosition === 'top' ? 40 : undefined
          } : undefined,
          grid: config.showGrid !== false && type !== 'pie' ? {
            left: config.legendPosition === 'left' ? '15%' : '3%',
            right: config.legendPosition === 'right' ? '15%' : '4%',
            bottom: config.legendPosition === 'bottom' ? '15%' : '3%',
            top: title ? 80 : 40,
            containLabel: true
          } : undefined,
          color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
        };

        // 根据图表类型生成不同的配置
        switch (type) {
          case 'bar':
            return {
              ...baseOption,
              xAxis: {
                type: 'category',
                data: data.categories || [],
                axisLabel: {
                  interval: 0,
                  rotate: (data.categories || []).some(cat => cat.length > 4) ? 45 : 0
                }
              },
              yAxis: {
                type: 'value',
                splitLine: {
                  show: config.showGrid !== false
                }
              },
              series: (data.series || []).map(s => ({
                name: s.name,
                type: 'bar',
                data: s.data || [],
                emphasis: {
                  focus: 'series'
                }
              }))
            };

          case 'line':
            return {
              ...baseOption,
              xAxis: {
                type: 'category',
                data: data.categories || [],
                boundaryGap: false
              },
              yAxis: {
                type: 'value',
                splitLine: {
                  show: config.showGrid !== false
                }
              },
              series: (data.series || []).map(s => ({
                name: s.name,
                type: 'line',
                data: s.data || [],
                smooth: true,
                emphasis: {
                  focus: 'series'
                }
              }))
            };

          case 'pie':
            return {
              ...baseOption,
              grid: undefined,
              series: [{
                name: (data.series && data.series[0]) ? data.series[0].name : '数据',
                type: 'pie',
                radius: ['0%', '70%'],
                center: ['50%', '60%'],
                data: (data.categories || []).map((cat, index) => ({
                  name: cat,
                  value: (data.series && data.series[0] && data.series[0].data) ? data.series[0].data[index] || 0 : 0
                })),
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }]
            };

          default:
            return baseOption;
        }
      };

      // 渲染图表
      if (node.attrs.data) {
        renderChart(chartContainer, node.attrs.data);
      } else {
        // 显示占位符
        chartContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #999;">点击编辑按钮配置图表</div>';
      }

      return {
        dom,
        update: (updatedNode) => {
          console.log('NodeView update called');
          console.log('Current node data:', node.attrs.data);
          console.log('Updated node data:', updatedNode.attrs.data);

          if (updatedNode.type.name !== 'chart') {
            console.log('Node type mismatch, skipping update');
            return false;
          }

          // 检查数据是否真的发生了变化
          if (updatedNode.attrs.data !== node.attrs.data) {
            console.log('Chart data changed, re-rendering');
            node = updatedNode; // 更新节点引用

            // 先清理旧的图表实例
            if (chartContainer._chartInstance) {
              console.log('Disposing old chart instance for update');
              chartContainer._chartInstance.dispose();
              chartContainer._chartInstance = null;
            }
            if (chartContainer._resizeObserver) {
              chartContainer._resizeObserver.disconnect();
              chartContainer._resizeObserver = null;
            }

            // 强制重新渲染图表
            setTimeout(() => {
              renderChart(chartContainer, updatedNode.attrs.data);
            }, 100); // 给DOM更多时间更新
          } else {
            console.log('Chart data unchanged, skipping re-render');
          }

          return true;
        },
        destroy() {
          console.log('NodeView destroy called');
          // 清理图表实例
          if (chartContainer._chartInstance) {
            chartContainer._chartInstance.dispose();
            chartContainer._chartInstance = null;
          }
          // 清理resize处理器
          if (chartContainer._resizeHandler) {
            window.removeEventListener('resize', chartContainer._resizeHandler);
            chartContainer._resizeHandler = null;
          }
          // 清理resize观察器
          if (chartContainer._resizeObserver) {
            chartContainer._resizeObserver.disconnect();
            chartContainer._resizeObserver = null;
          }
        }
      };
    };
  },

  renderChart(container, dataString) {
    console.log('renderChart called with:', dataString);

    if (!dataString) {
      container.innerHTML = '<div style="padding: 20px; text-align: center; color: #999;">点击编辑按钮配置图表</div>';
      return;
    }

    try {
      const chartData = JSON.parse(dataString);
      console.log('Parsed chart data:', chartData);

      // 验证基本数据结构
      if (!chartData.type) {
        throw new Error('Missing chart type');
      }

      if (!chartData.data) {
        throw new Error('Missing chart data');
      }

      // 对于饼图等特殊类型，数据结构可能不同
      if (chartData.type === 'pie' || chartData.type === 'doughnut') {
        if (!chartData.data.series || !Array.isArray(chartData.data.series)) {
          throw new Error('Invalid pie chart data structure');
        }
      } else {
        // 对于其他图表类型，需要categories和series
        if (!chartData.data.categories || !Array.isArray(chartData.data.categories)) {
          throw new Error('Missing or invalid categories data');
        }
        if (!chartData.data.series || !Array.isArray(chartData.data.series)) {
          throw new Error('Missing or invalid series data');
        }
      }

      // 异步加载 ECharts 并渲染
      this.loadEChartsAndRender(container, chartData);

    } catch (error) {
      console.error('Chart data parsing error:', error);
      container.innerHTML = `<div style="padding: 20px; text-align: center; color: #ff4d4f;">图表数据解析错误: ${error.message}</div>`;
    }
  },

  async loadEChartsAndRender(container, chartData) {
    try {
      console.log('loadEChartsAndRender called with:', chartData);

      // 先清理旧的图表实例
      if (container._chartInstance) {
        console.log('Disposing old chart instance');
        container._chartInstance.dispose();
        container._chartInstance = null;
      }
      if (container._resizeObserver) {
        container._resizeObserver.disconnect();
        container._resizeObserver = null;
      }

      // 检查是否已经有全局的 ECharts
      let echarts;
      if (window.echarts) {
        echarts = window.echarts;
      } else {
        // 动态导入 ECharts
        const echartsModule = await import('echarts');
        echarts = echartsModule.default || echartsModule;
        // 缓存到全局
        window.echarts = echarts;
      }

      // 清空容器
      container.innerHTML = '';

      // 确保容器有正确的尺寸
      container.style.width = '100%';
      container.style.height = '400px';
      container.style.minHeight = '400px';
      container.style.display = 'block';

      // 等待DOM更新和容器尺寸稳定
      await new Promise(resolve => {
        // 使用多重等待确保容器完全准备好
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            setTimeout(resolve, 150);
          });
        });
      });

      // 再次确认容器尺寸
      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        console.warn('Container still has zero dimensions, forcing size');
        container.style.width = '100%';
        container.style.height = '400px';
        container.style.minHeight = '400px';
        container.style.display = 'block';

        // 再等待一下
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 创建新的图表实例
      const chart = echarts.init(container, null, {
        renderer: 'canvas',
        useDirtyRect: false
      });

      // 生成 ECharts 配置
      const option = this.generateEChartsOption(chartData);
      console.log('Generated ECharts option:', option);

      // 设置图表配置
      chart.setOption(option, true); // true 表示不合并，完全替换

      // 多次强制重新渲染，确保图表正确显示
      const forceResize = () => {
        if (chart && !chart.isDisposed()) {
          chart.resize();
        }
      };

      // 立即resize一次
      setTimeout(forceResize, 50);
      // 再次resize确保稳定
      setTimeout(forceResize, 200);
      // 最后一次resize
      setTimeout(forceResize, 500);

      // 响应式处理
      const resizeObserver = new ResizeObserver(() => {
        if (chart && !chart.isDisposed()) {
          chart.resize();
        }
      });
      resizeObserver.observe(container);

      // 存储图表实例以便后续清理
      container._chartInstance = chart;
      container._resizeObserver = resizeObserver;

      console.log('Chart rendered successfully');

    } catch (error) {
      console.error('ECharts loading error:', error);
      container.innerHTML = '<div style="padding: 20px; text-align: center; color: #ff4d4f;">图表加载失败: ' + error.message + '</div>';
    }
  },

  generateEChartsOption(chartData) {
    const { type, title, data, config } = chartData;
    
    const baseOption = {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: config.showTooltip !== false ? {
        trigger: type === 'pie' || type === 'doughnut' ? 'item' : 'axis',
        backgroundColor: 'rgba(50,50,50,0.95)',
        borderColor: '#333',
        borderWidth: 1,
        textStyle: {
          color: '#fff',
          fontSize: 12
        },
        confine: true,
        appendToBody: true,
        enterable: false,
        hideDelay: 0,
        showDelay: 0,
        transitionDuration: 0,
        extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.15); border-radius: 4px; z-index: 9999 !important; pointer-events: none !important;'
      } : undefined,
      legend: config.showLegend !== false ? {
        orient: config.legendPosition === 'left' || config.legendPosition === 'right' ? 'vertical' : 'horizontal',
        left: config.legendPosition === 'left' ? 0 :
              config.legendPosition === 'right' ? 'right' : 'center',
        right: config.legendPosition === 'right' ? 0 : undefined,
        bottom: config.legendPosition === 'bottom' ? 0 : undefined,
        top: config.legendPosition === 'top' ? 40 :
             (config.legendPosition === 'left' || config.legendPosition === 'right') ?
             (config.legendVerticalPosition === 'top' ? 40 :
              config.legendVerticalPosition === 'bottom' ? 'bottom' : 'middle') : undefined
      } : undefined,
      grid: config.showGrid !== false && type !== 'pie' && type !== 'doughnut' && type !== 'radar' ? {
        left: config.legendPosition === 'left' ? '15%' : '3%',
        right: config.legendPosition === 'right' ? '15%' : '4%',
        bottom: config.legendPosition === 'bottom' ? '15%' : '3%',
        top: config.legendPosition === 'top' || title ? 80 : 40,
        containLabel: true
      } : undefined,
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
    };

    switch (type) {
      case 'bar':
        return {
          ...baseOption,
          xAxis: {
            type: 'category',
            data: data.categories,
            axisLabel: {
              interval: 0,
              rotate: data.categories.some(cat => cat.length > 4) ? 45 : 0
            }
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: config.showGrid !== false
            }
          },
          series: data.series.map(s => ({
            name: s.name,
            type: 'bar',
            data: s.data,
            emphasis: {
              focus: 'series'
            },
            animationDelay: (idx) => idx * 10
          }))
        };

      case 'horizontalBar':
        return {
          ...baseOption,
          xAxis: {
            type: 'value',
            splitLine: {
              show: config.showGrid !== false
            }
          },
          yAxis: {
            type: 'category',
            data: data.categories,
            axisLabel: {
              interval: 0
            }
          },
          series: data.series.map(s => ({
            name: s.name,
            type: 'bar',
            data: s.data,
            emphasis: {
              focus: 'series'
            },
            animationDelay: (idx) => idx * 10
          }))
        };

      case 'line':
        return {
          ...baseOption,
          xAxis: {
            type: 'category',
            data: data.categories,
            boundaryGap: false
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: config.showGrid !== false
            }
          },
          series: data.series.map(s => ({
            name: s.name,
            type: 'line',
            data: s.data,
            smooth: true,
            emphasis: {
              focus: 'series'
            },
            animationDelay: (idx) => idx * 10
          }))
        };

      case 'area':
        return {
          ...baseOption,
          xAxis: {
            type: 'category',
            data: data.categories,
            boundaryGap: false,
            axisLine: {
              show: true
            },
            axisTick: {
              show: true
            }
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: config.showGrid !== false
            },
            axisLine: {
              show: true
            },
            axisTick: {
              show: true
            }
          },
          series: data.series.map((s, index) => ({
            name: s.name,
            type: 'line',
            data: s.data,
            smooth: true,
            areaStyle: {
              opacity: 0.6
            },
            lineStyle: {
              width: 2
            },
            symbol: 'circle',
            symbolSize: 6,
            emphasis: {
              focus: 'series',
              areaStyle: {
                opacity: 0.8
              }
            },
            animationDelay: (idx) => idx * 10 + index * 100
          }))
        };

      case 'pie':
        return {
          ...baseOption,
          grid: undefined,
          series: [{
            name: data.series[0]?.name || '数据',
            type: 'pie',
            radius: ['0%', '70%'],
            center: ['50%', '60%'],
            data: data.categories.map((cat, index) => ({
              name: cat,
              value: data.series[0]?.data[index] || 0
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: (idx) => Math.random() * 200
          }]
        };

      case 'doughnut':
        return {
          ...baseOption,
          grid: undefined,
          series: [{
            name: data.series[0]?.name || '数据',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '60%'],
            data: data.categories.map((cat, index) => ({
              name: cat,
              value: data.series[0]?.data[index] || 0
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: (idx) => Math.random() * 200
          }]
        };

      case 'scatter':
        return {
          ...baseOption,
          xAxis: {
            type: 'value',
            splitLine: {
              show: config.showGrid !== false
            }
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: config.showGrid !== false
            }
          },
          series: data.series.map(s => ({
            name: s.name,
            type: 'scatter',
            data: s.data.map((value, index) => [index, value]),
            symbolSize: 8,
            emphasis: {
              focus: 'series'
            },
            animationDelay: (idx) => idx * 5
          }))
        };

      case 'radar':
        const indicators = data.categories.map(cat => ({ name: cat, max: Math.max(...data.series.map(s => Math.max(...s.data))) * 1.2 }));
        return {
          ...baseOption,
          grid: undefined,
          radar: {
            indicator: indicators,
            center: ['50%', '60%'],
            radius: '60%'
          },
          series: [{
            name: data.series[0]?.name || '数据',
            type: 'radar',
            data: data.series.map(s => ({
              name: s.name,
              value: s.data
            })),
            emphasis: {
              focus: 'series'
            },
            animationDelay: (idx) => idx * 10
          }]
        };

      default:
        console.warn('Unknown chart type:', type, 'falling back to bar chart');
        // 默认回退到柱状图
        return {
          ...baseOption,
          xAxis: {
            type: 'category',
            data: data.categories || [],
            axisLabel: {
              interval: 0,
              rotate: (data.categories || []).some(cat => cat.length > 4) ? 45 : 0
            }
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: config.showGrid !== false
            }
          },
          series: (data.series || []).map(s => ({
            name: s.name,
            type: 'bar',
            data: s.data || [],
            emphasis: {
              focus: 'series'
            },
            animationDelay: (idx) => idx * 10
          }))
        };
    }
  }
});