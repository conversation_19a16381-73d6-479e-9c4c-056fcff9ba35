目标与范围

- 将“图表（ECharts）”作为一个一等块节点引入编辑器，提供插入、编辑、导出（HTML 保留可视化、Word 嵌入静态图）、协同独占编辑与良好性能。
- 首批图表类型：柱状图、条形图、折线图、面积图、饼/环图、散点图、雷达图；不含堆叠/平滑/多 Y 轴/双坐标系等增强。
- 数据编辑：基础模式（结构化配置 + 类 Excel 简易行列输入）；高级模式（直接编辑 ECharts option）。
- 样式配置首版：主题色、图例、轴、标签、提示框、网格、边距、圆角。
- 菜单入口：工具栏、斜杠菜单均可插入并打开编辑面板。
- 图标沿用 IIcon 机制；国际化中英双语；ECharts 按需加载/注册。

现有基础与锚点（不改动，仅作为集成点）

- TipTap Vue NodeView/Renderer能力： `render.js`
- 模态框插件（open/close）： `index.js`
- IIcon 组件与图标集中注册： `icon.js` `icon.ts`
- i18n 基座（核心 t/addLocale）： `index.js`
- 导出入口页面（HTML/Docx 选项与 exportHtml 钩子）： `notion-page.vue`
- 协同基座（Yjs + y-websocket）：同上 `notion-page.vue`

一、数据结构与节点 schema 规范（强化）

- 新增节点名：chart
- 分组与行为：group=block，atom=true，selectable=true，draggable=true，defining=true（便于选择/移动/删除）。
- 存储规范：严格采用 schema attributes 纯 JSON 存储，禁止在节点内嵌入函数/循环引用/运行时代码；仅持久化数据与配置，不持久化运行时实例。基础/高级模式均遵循此规范。
- attrs 设计（最小持久化集）：
  - id: string（唯一标识，参与独占锁、快照映射）
  - type: "bar" | "barHorizontal" | "line" | "area" | "pie" | "doughnut" | "scatter" | "radar"
  - dataset: { fields: string[]; rows: (string|number)[][] }（基础模式）
  - option: object | null（高级模式 ECharts option；为空代表由 dataset + preset 计算 option）
  - style: { theme: string; legend: {...}; axis: {...}; label: {...}; tooltip: {...}; grid: {...}; padding: number|{top/right/bottom/left}; radius: number|[number, number] }（聚焦高频项）
  - size: { width?: number; height?: number }
  - snapshot?: string（base64 图像，用于 HTML/Word 导出兜底）
  - mode: "basic" | "advanced"
  - version: number（配置版本，便于未来迁移）
- JSON 大小控制策略：
  - 基础模式仅存 dataset/style；运行时由适配层生成 option；
  - 高级模式存完整 option，但导出/保存时生成 snapshot；
  - 防御性限制 option 最大深度与字段白名单（避免注入风险）。

二、扩展文件与类/函数（新增）

- 新增 TipTap 扩展： `chart.js`
  - 提供 schema、parseHTML/toDOM、commands：setChart(attrs)、updateChart(attrs)、editChart()、setChartSize({width,height})。
  - NodeViewRenderer 集成到 Vue 渲染（见下一节）。
  - 错误处理：当 attrs 校验失败或 option 解析异常时，降级渲染“占位空态 + 错误提示”，并可一键打开编辑面板。
- 新增 ECharts 适配层： `echarts-adapter.js`
  - export: ensureEcharts()（懒加载 echarts/core + 按需 charts/components/renderers）、makeOptionFromDataset(type, dataset, style)、applyOption(instance, option)。
  - 仅注册首批图表对应模块；按需动态 import，失败时回退提示。
- 新增 NodeView 组件： `chart-view.js`
  - 职责：容器挂载 ECharts、ResizeObserver 自适应、右上角悬浮编辑/删除/尺寸手柄、选中态 outline。
  - 生命周期：mounted 懒加载 echarts 并实例化；update 时最小 diff 更新；destroy 时 dispose。
  - 提供获取 snapshot 的方法（getDataURL）。
  - 与独占锁联动：若节点被他人锁定，遮罩并禁用交互。
- 新增编辑面板组件（JS 组件风格以对齐项目）： `chart-edit-panel.js`
  - 基础/高级模式 tab；
  - 结构化配置（类型、主题色、图例、轴、标签、提示框、网格、边距、圆角）；
  - 类 Excel 行列编辑（最少依赖，沿用项目 UI/Trigger/Input 体系）；
  - 高级模式 JSON（option）编辑 + 即时预览；
  - 确认/取消回调。
- 新增（可选）样式： `chart-editor.scss`
- 新增协同独占锁插件： `collab-lock.js`
  - 集成 y-websocket awareness，维护 { nodeId: { clientId, name, ts } }；
  - 提供 acquireLock(nodeId)、releaseLock(nodeId)、isLocked(nodeId)、subscribe(cb)；
  - 锁为非持久化（仅在会话间传播）；Tab/连接断开自动释放。

三、Vue NodeView 集成

- 使用现有渲染器：在扩展中通过 VueNodeViewRenderer 绑定 `render.js` 。
- NodeView 交互：
  - 编辑按钮：调用 editChart()，打开模态并挂载编辑面板；
  - 删除按钮：commands.deleteSelection() 或 deleteNode；
  - 尺寸手柄：拖拽变更 size 并存 attrs；结束时保存 snapshot。
- 容错：ECharts 初始化失败时，展示错误空态并暴露“重试/切基础模式”操作。

四、编辑面板与模态承载

- 复用模态插件： `index.js` 的 modal.open(title, content, onCancel, onConfirm)。
- 打开策略：传入占位 div 作为 content 字符串，open 后在 content 容器内 programmatic mount chart-edit-panel 组件并接管确认为回调。
- 关闭与清理：在 cancel/confirm 时卸载组件实例并释放锁。

五、菜单集成（工具栏/气泡/斜杠）

- 工具栏：在 `toolbar-menu.js` 的 TOOLBAR_MENU_SORT 中新增“插入图表”项；点击后执行 editor.chain().focus().setChart(defaultAttrs).run() 并立即打开编辑面板。
- 气泡菜单：在 `bubble-menu-selector.js` 的通用插入组中新增“图表”入口（在纯文本/空段落选区出现）。
- 斜杠菜单：在 `slash-menu.js` 注册 chart 命令项（name、keywords、icon、command、isActive）。

六、图标与 i18n

- 图标：在 `icon.ts` 增加 name="chart" 的 SVG path；IIcon 使用 name="chart" 即可。
- 文案：通过 `index.js` 的 addLocale 注入 chart.* 词条（zh/en），覆盖菜单项、面板字段、提示信息、错误与空态等。

七、导出策略（细化）

- 静态 HTML（SSR/服务端导出）：
  - 默认用 attrs.snapshot 渲染 <img>，确保离线可见；
  - 可选最小运行时：在导出时内联极小脚本 + option 重放，但首版以 snapshot 为准以降低复杂度。
- Word（DOCX）：
  - 使用 ECharts getDataURL({ pixelRatio, backgroundColor }) 获取 PNG base64；
  - 将图片数据作为文档图片资源注入（在 `notion-page.vue` 的 docx 分支里新增 exportDocx 流程，并遍历文档节点收集 snapshot）。
- 快照生成时机：
  - 用户点击确认保存时更新 attrs.snapshot；
  - 导出前兜底重新抓取（若有实例可见）。

八、协同与独占编辑（强化）

- 锁范围：chart 节点级别（基于 attrs.id）。
- 获取锁：打开编辑面板前尝试 acquireLock(id)；失败则提示“某某正在编辑”，仅只读预览；
- 释放锁：面板关闭、Tab 关闭、网络断开、进程异常均自动释放（基于 awareness 清理与心跳超时）。
- 与 Yjs 文档更新：编辑过程中仅在本地预览；点击确认时一次性 updateChart(attrs) 提交，减少冲突。撤销/重做不影响锁语义。

九、性能与安全（强化）

- 动态 import echarts/core 与具体图表/组件/renderer；按需注册。
- NodeView 层做最小 diff：仅当 type/dataset/style/size 变更才更新 option 或 resize。
- IntersectionObserver 推迟创建实例；隐藏时销毁或暂停（视 UX 决策）。
- ResizeObserver 节流；编辑面板预览也做防抖。
- 输入校验：
  - 高级模式 JSON 深度与字段白名单，阻断 function/循环引用；
  - dataset 表格限制行列上限与单元格类型清洗。

十、依赖管理与版本兼容（新增约束）

- 新增依赖：echarts（v5+）。按需导入核心与子包，不引入全量构建。
- 禁止引入 `@tiptap/vue-3` v3.x 与 `@tiptap/extension-mention` v3.x；项目统一使用 TipTap v2 生态（如 `@tiptap/core@^2.26.1`）。
- 复用现有自定义 Vue NodeView/Renderer 与扩展机制，完全替代 v3 版本依赖带来的渲染/菜单/建议系统差异。
- 不引入额外重量级 UI 或表格库，优先复用现有 `trigger.js`、ColorPicker、Button、Tooltip 等。

十一、测试与验收

- 功能回归：
  - 插入/编辑/删除/撤销重做；切换类型、修改数据与样式；面板基础/高级切换；生成 snapshot。
- 导出：
  - HTML 输出离线可见；DOCX 嵌入 PNG，清晰度与主题色正确。
- 协同：
  - 两端并发：一端编辑锁生效，另一端只读；释放后可抢占。
- 性能：
  - 首次插入时的懒加载时间可接受；多图文档滚动与编辑流畅（内存无明显泄漏）。

更改计划

- 文件（新增）
  - f:/Code/px-doc/app/src/packages/core/extensions/chart.js（TipTap 扩展：schema/commands/toDOM/parseHTML/NodeViewRenderer）
  - f:/Code/px-doc/app/src/packages/vue3/utils/echarts-adapter.js（ECharts 懒加载与 option 适配）
  - f:/Code/px-doc/app/src/packages/vue3/components/node-view/chart-view.js（NodeView 组件）
  - f:/Code/px-doc/app/src/packages/vue3/components/chart/chart-edit-panel.js（编辑面板组件：基础/高级）
  - f:/Code/px-doc/app/src/packages/vue3/styles/chart/chart-editor.scss（样式，若必要）
  - f:/Code/px-doc/app/src/packages/core/plugins/collab-lock.js（协同独占锁）
- 文件（修改）
  - f:/Code/px-doc/app/src/packages/vue3/components/toolbar-menu/toolbar-menu.js（新增“图表”菜单项）
  - f:/Code/px-doc/app/src/packages/vue3/components/bubble-menu/selector/bubble-menu-selector.js（新增“图表”菜单项）
  - f:/Code/px-doc/app/src/packages/vue3/components/slash-menu/slash-menu.js（新增斜杠命令）
  - f:/Code/px-doc/app/src/utils/icon.ts（注册 chart 图标）
  - f:/Code/px-doc/app/src/packages/core/locales/index.js（addLocale 注入 chart 词条；或在加载期动态注入）
  - f:/Code/px-doc/app/src/views/notion-page/notion-page.vue（导出分支：实现 DOCX 使用 snapshot；HTML 导出优先使用 snapshot）
- 理由
  - 将图表作为原生块节点融入编辑器生态；提供跨菜单入口与统一编辑体验；保证导出可视化稳定及协同编辑安全；以懒加载/按需注册控制包体与性能。

错误处理策略

- NodeView 初始化或 option 应用失败：显示空态与“重试/切基础模式”按钮，不阻断文档渲染。
- 模态关闭时若未确认，则不落盘；若确认失败（校验/序列化错误），提示原因并保持面板。
- 导出时若无法生成最新 snapshot，回退使用已存 attrs.snapshot；若 snapshot 缺失，则在 HTML 导出中内联文本提示，在 DOCX 导出中嵌入占位图。
- 协同锁异常（网络/断线）：以超时策略释放，避免幽灵锁。

依赖与兼容

- 依赖：echarts@^5（仅 core + 按需模块）；对 node/esbuild 兼容当前构建链。
- 兼容：保留现有 iframe、table、image 等节点行为；不破坏 Toolbar/Bubble/Slash 的现有结构与排序。

实施清单

1. 1.
   新增 TipTap 扩展文件 packages/core/extensions/chart.js，定义 schema/attrs/parseHTML/toDOM/commands，并挂载 VueNodeViewRenderer（不实现 UI，只定义接口与默认 attrs）。
2. 2.
   新增 ECharts 适配层 packages/vue3/utils/echarts-adapter.js，封装 ensureEcharts、makeOptionFromDataset、applyOption（按需注册与懒加载设计）。
3. 3.
   新增 NodeView 组件 packages/vue3/components/node-view/chart-view.js，打通 ECharts 实例生命周期、ResizeObserver、悬浮操作（编辑/删除/尺寸）。
4. 4.
   新增编辑面板 packages/vue3/components/chart/chart-edit-panel.js，完成基础/高级模式表单与类 Excel 输入、预览区联动与校验策略。
5. 5.
   集成模态：在 chart 扩展的 editChart() 与 NodeView 编辑入口中，调用 modal.open 并在 content 容器挂载编辑面板；确认后回写 attrs + 生成 snapshot。
6. 6.
   菜单集成：修改 toolbar-menu、bubble-menu-selector、slash-menu，新增“图表”项（IIcon=chart），调用 setChart(defaultAttrs) + 自动打开编辑面板。
7. 7.
   图标注册：在 utils/icon.ts 增加 chart 图标资源并验证 IIcon 渲染。
8. 8.
   i18n：通过 core/locales/index.js 的 addLocale 注入 chart.*（zh/en）词条，覆盖菜单名、面板字段、错误提示。
9. 9.
   协同独占锁：新增 packages/core/plugins/collab-lock.js；在 notion-page.vue 初始化 provider 后注册；在打开/关闭编辑面板时 acquire/release；NodeView 根据锁态遮罩。
10. 10.
    导出：在 notion-page.vue 的导出流程中，HTML 使用 <img> 渲染；DOCX 分支新增 exportDocx，遍历文档抽取 snapshot 并嵌入 PNG。
11. 11.
    性能优化：IntersectionObserver 懒实例化、渲染节流、最小 diff 更新、ECharts 模块按需注册验证。
12. 12.
    回归自测：功能/导出/协同/性能四类用例跑通；修正边界问题。
13. 13.
    文档与记录：在 CHANGELOG.md 追加“完成条目”，同步任务清单状态。

附录：任务清单（规划执行）

- [ ] 明确并落地“schema attributes 纯 JSON 存储”规范（含校验与白名单）
- [ ] 完成首批图表类型（柱状/条形/折线/面积/饼-环/散点/雷达）的数据-样式-适配实现
- [ ] 编辑面板：结构化面板 + 类Excel 数据编辑 + 高级 JSON 模式 + 预览
- [ ] 样式首版：主题色、图例、轴、标签、提示框、网格、边距、圆角
- [ ] 导出：HTML 用 snapshot；DOCX 嵌入 PNG；导出前兜底抓取
- [ ] 协同：独占锁 acquire/release；只读遮罩与提示
- [ ] 菜单：工具栏/斜杠/（必要时）气泡菜单入口
- [ ] 图标与 i18n：IIcon=chart；zh/en 词条
- [ ] 性能：ECharts 按需注册与懒加载；最小 diff；观察者节流
- [ ] 依赖与版本：禁止引入 TipTap v3 依赖，统一 v2 生态

任务记录

- 2025-08-26：完善规划，明确 JSON 存储规范、类型清单、编辑与样式范围、导出策略、独占协同、性能与版本约束，新增任务清单与记录栏目。