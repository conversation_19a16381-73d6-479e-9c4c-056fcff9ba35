import { defineComponent, computed, h } from "vue";
import { prefixClass, t } from "../../../core";
import { <PERSON><PERSON><PERSON><PERSON>, IDivider, ITooltip, IIcon } from "../ui";
import { isString, isObject } from "../../utils";
import ButtonLink from "../special-button/button-link";
import ButtonTextAlign from "../special-button/button-text-align";
import ButtonColor from "../special-button/button-color";
import ButtonBackground from "../special-button/button-background";
import ButtonFontFamily from "../special-button/button-font-family";
import ButtonFontSize from "../special-button/button-font-size";
import ButtonTable from "../special-button/button-table";
import ButtonImage from "../special-button/button-image";
import Button<PERSON>hart from "../special-button/button-chart";

const TOOLBAR_MENU_SORT = [
  "history",
  "|",
  "textClear",
  "|",
  "fontFamily",
  "fontSize",
  "|",
  "bold",
  "italic",
  "underline",
  "strike",
  "code",
  "subscript",
  "superscript",
  "|",
  "color",
  "background",
  "textAlign",
  "hardBreak",
  "indent",
  "outdent",
  "|",
  "image",
  "table",
  "chart",
  "link",
  "blockquote",
  "codeBlock",
  "|",
  "bulletList",
  "orderedList",
  "taskList",
  "|",
  "divider",
];

export default defineComponent({
  name: "PxEditorToolbar",
  props: {
    editor: {
      type: Object,
      required: true,
    },
    sort: {
      type: Array,
      default: () => TOOLBAR_MENU_SORT,
    },
    uploadFn: {
      type: Function,
      default: () => null,
    }
  },
  setup(props, { slots }) {
    const slotPrefix = slots["prefix"];
    const slotSuffix = slots["suffix"];

    const toolbarMenus = computed(() => {
      return props.sort
        .map((menu) => {
          if (isString(menu)) {
            if (menu === "|") return { name: "|" };
            if (menu === "textClear") {
              return {
                name: "textClear",
                command: ({ editor }) =>
                  editor.chain().focus().unsetAllMarks().run(),
                isActive: () => null,
              };
            }

            const extension = props.editor.extensionManager.extensions.find(
              (ext) => ext.name === menu,
            );
            if (extension) {
              return {
                name: menu,
                ...extension?.options,
              };
            }

            return null;
          }

          if (isObject(menu)) {
            return menu;
          }
        })
        .filter(Boolean);
    });

    return () =>
      h("div", { class: `${prefixClass}-toolbar-menu` }, [
        slotPrefix && slotPrefix({ editor: props.editor }),
        toolbarMenus.value.map((menu) => {
          // 检查是否存在对应的具名插槽
          const slotName = slots[menu.name];

          if (slotName) {
            // 如果存在具名插槽，使用插槽内容
            return slotName({
              editor: props.editor,
              ...menu,
            });
          }

          if (menu.name === "|") {
            return h(IDivider, {
              type: "vertical",
              style: { height: "1.5rem" },
            });
          }

          if (menu.name === "history" || menu.name === "indent") {
            return menu.list.map((item) =>
              h(
                ITooltip,
                { text: t(item.name), shortcutkeys: item.shortcutkeys },
                {
                  default: () =>
                    h(
                      IButton,
                      {
                        active:
                          item?.isActive &&
                          item?.isActive({ editor: props.editor }),
                        disabled:
                          item?.isDisabled &&
                          item?.isDisabled({ editor: props.editor }),
                        onClick: () => item.command({ editor: props.editor }),
                      },
                      {
                        icon: () =>
                          h(IIcon, {
                            name: item.name,
                            size: 14,
                          }),
                      },
                    ),
                },
              ),
            );
          }

          if (menu.name === "link") {
            return h(ButtonLink, {
              editor: props.editor,
              menu,
              toolbar: true,
            });
          }

          if (menu.name === "textAlign") {
            return h(ButtonTextAlign, {
              editor: props.editor,
              menu,
            });
          }

          if (menu.name === "color") {
            return h(ButtonColor, {
              editor: props.editor,
              menu,
            });
          }

          if (menu.name === "background") {
            return h(ButtonBackground, {
              editor: props.editor,
              menu,
            });
          }

          if (menu.name === "fontFamily") {
            return h(ButtonFontFamily, {
              editor: props.editor,
              menu,
            });
          }

          if (menu.name === "fontSize") {
            return h(ButtonFontSize, {
              editor: props.editor,
              menu,
            });
          }

          if (menu.name === "image") {
            return h(ButtonImage, {
              editor: props.editor,
              menu,
              uploadFn: props.uploadFn
            });
          }

          if (menu.name === "table") {
            return h(ButtonTable, {
              editor: props.editor,
              menu,
            });
          }

          if (menu.name === "chart") {
            return h(ButtonChart, {
              editor: props.editor,
              menu,
            });
          }

          return h(
            ITooltip,
            { text: t(menu.name), shortcutkeys: menu.shortcutkeys },
            {
              default: () =>
                h(
                  IButton,
                  {
                    active:
                      menu?.isActive &&
                      menu?.isActive({ editor: props.editor }),
                    disabled:
                      menu?.isDisabled &&
                      menu?.isDisabled({ editor: props.editor }),
                    onClick: () => menu.command({ editor: props.editor }),
                  },
                  {
                    icon: () =>
                      h(IIcon, {
                        name: menu.name,
                        size: 14,
                      }),
                  },
                ),
            },
          );
        }),
        slotSuffix && slotSuffix({ editor: props.editor }),
      ]);
  },
});
