# px-doc 文档系统（Vue3 + TipTap + Yjs）

本文档介绍项目的核心技术分析、协同版本管理亮点、开发日志与待开发清单。

## 核心技术分析与亮点

### 1. 协同编辑（Yjs + y-websocket + TipTap）
- 使用 Yjs 管理协同状态，通过 y-websocket 进行实时同步。
- 客户端编辑器基于 TipTap（ProseMirror），启用 `Collaboration` 与 `CollaborationCursor` 扩展，实现多人实时编辑与光标显示。
- 服务器端提供 y-websocket 兼容服务，并支持 `.ydoc` 文件持久化（`server/doc-storage/*.ydoc`）。

亮点：
- 低延迟、高并发协同；网络抖动下有乐观合并与离线恢复能力。
- 可与版本系统协同工作，允许“回滚”并广播到所有在线客户端。

### 2. 版本管理（列表/创建/删除/编辑/预览/恢复）
- 版本列表：分页查询，显示作者、时间、大小、类型（自动/手动）。
- 保存版本：支持手动（按钮/快捷键）与自动（定时）保存，自动保存版本不可删除。
- 版本持久化：后端以“文件持久化”记录到 `server/db/version/<docId>.json`，重启不丢。
- 预览版本：创建只读临时 TipTap Editor，将版本 JSON 渲染为 HTML 展示（非纯 JSON 文本）。
- 恢复版本：在协同场景中使用编辑器命令进行恢复，后续将升级为 Y.Doc 事务（见下文）。

亮点：
- 预览更接近真实渲染效果，便于对比和确认。
- 支持自动恢复：当协同连接完成且文档为空时，自动加载“最近版本”，避免用户手动恢复。

### 3. 文档元数据持久化（文件方案）
- 文档列表数据持久化到 `server/db/document/documents.json`。
- `GET/POST/PUT/DELETE /documents` 对文件进行增删改查，重启后仍保留。

亮点：
- 无需引入数据库即可快速落地持久化，支持日后平滑迁移到 DB。

### 4. 协同场景下的“版本恢复”设计（即将落地）
问题：协同模式下，编辑器内容由 Y.Doc 驱动，仅调用 `editor.commands.setContent()` 可能与远端同步产生覆盖或非原子状态。

目标：通过 “Y.Doc 事务” 原子地写入版本内容并广播给所有客户端，保证回滚的一致性与可见性。

方案：
- 在前端使用 `doc.transact(() => editor.commands.setContent(json, false), 'restore-version')` 包裹恢复逻辑。
- 优点：
  - 原子性：一次事务一次 Y 更新，避免中间态。
  - 一致性：更新直达协同源头，所有客户端收到同一更新。
  - 可控性：可标注事务 `origin`，与历史/撤销策略对齐。
- 备选（更底层）：直接操作 `Y.XmlFragment` 写入结构（维护成本较高，默认不采用）。

验收标准：
- 多个会话同时打开同一文档，执行“恢复版本”后所有会话在短时间内看到同样内容；无“恢复被覆盖”的闪动；撤销/重做不被碎片化污染。

---

## 开发日志（从本次协作开始）

1. 版本恢复失效排查与修复（前端）
   - 原因：协同模式下仅改本地响应式变量不会触发编辑器更新。
   - 调整：通过 `editor.commands.setContent()` 恢复，并完善格式处理（JSON/HTML）。

2. 新建文档持久化（服务端）
   - 将文档列表改为文件持久化：`server/db/document/documents.json`。
   - `server/src/router/document.js` 新增 `loadDocuments/saveDocuments`。

3. 版本预览渲染 HTML（前端）
   - 在 `VersionManager.vue` 使用只读临时 TipTap Editor + `NotionKit` 渲染 JSON → HTML。
   - 解决缺失包报错（移除对 `@tiptap/extension-paragraph` 等动态图包的依赖）。

4. 版本记录持久化（服务端）
   - 每个文档一个文件 `server/db/version/<docId>.json`。
   - 路由 `GET/POST/PUT/DELETE` 改为文件读写，重启不丢。

5. 自动恢复最近版本（前端）
   - 在协同 provider `synced` 事件后判断编辑器是否为空；为空则自动拉取最近版本并恢复。
   - 避免服务器重启后文档空白、需要手动恢复的问题。

6. 协同正文持久化（部署建议）
   - 推荐使用 `src/index_v1.js + lib/ydoc.js` 启用 `.ydoc` 文件持久化（若未启用，可按部署要求切换）。

---

## Roadmap

请查看项目根目录的 `ROADMAP.md` 获取最新的待开发清单与优先级。

---

## 项目启动

### 安装依赖
```sh
pnpm install
```

### 启动前端（默认为 http://localhost:9999）
```sh
pnpm dev
```

### 启动服务端（开发环境）
按你的实际脚本运行（示例）：
```sh
# Linux / macOS（示例，按你的实际脚本为准）
npm start

# Windows（示例，按你的实际脚本为准）
npm run start:win
```

---

## 许可证
MIT
