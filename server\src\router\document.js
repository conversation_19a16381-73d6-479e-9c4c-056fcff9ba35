// 文档列表持久化改造：使用文件进行持久化存储
import fs from 'fs'
import { resolve, dirname } from 'path'

const DOCS_FILE = resolve(__dirname, '../../db/document/documents.json')

function ensureDirExists(path) {
  try {
    fs.mkdirSync(dirname(path), { recursive: true })
  } catch (e) {}
}

function getDefaultDocuments() {
  const now = new Date().toISOString()
  return [
    { id: 'test', name: '测试文档', createdAt: now, updatedAt: now },
    { id: 'experiment', name: '实验文档', createdAt: now, updatedAt: now },
    { id: 'tutorial', name: '教学H5文档教程', createdAt: now, updatedAt: now },
    { id: 'math', name: '数学课件设计', createdAt: now, updatedAt: now },
  ]
}

function loadDocuments() {
  try {
    if (!fs.existsSync(DOCS_FILE)) {
      ensureDirExists(DOCS_FILE)
      const defaults = getDefaultDocuments()
      fs.writeFileSync(DOCS_FILE, JSON.stringify(defaults, null, 2), 'utf-8')
      return defaults
    }
    const raw = fs.readFileSync(DOCS_FILE, 'utf-8')
    const data = JSON.parse(raw)
    if (Array.isArray(data)) return data
    return getDefaultDocuments()
  } catch (e) {
    console.error('读取文档列表失败，使用默认数据。错误：', e)
    return getDefaultDocuments()
  }
}

function saveDocuments(docs) {
  try {
    ensureDirExists(DOCS_FILE)
    fs.writeFileSync(DOCS_FILE, JSON.stringify(docs, null, 2), 'utf-8')
    return true
  } catch (e) {
    console.error('保存文档列表失败：', e)
    return false
  }
}

export default (router, prefix) => {
  // 确保文档持久化文件在服务启动时就创建好
  try { loadDocuments() } catch (e) {}
  // 获取文档列表
  router.get(`${prefix}/documents`, async (ctx) => {
    try {
      const documents = loadDocuments()
      console.log('获取文档列表，当前文档数量:', documents.length);
      
      ctx.body = {
        code: 200,
        data: documents,
        message: '获取文档列表成功'
      };
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取文档列表失败',
        error: error.message
      };
    }
  });

  // 创建新文档
  router.post(`${prefix}/documents`, async (ctx) => {
    try {
      console.log('收到创建文档请求:', ctx.request.body);
      const { name } = ctx.request.body;
      
      if (!name) {
        console.log('文档名称为空');
        ctx.body = {
          code: 400,
          message: '文档名称不能为空'
        };
        return;
      }

      // 生成文档ID
      const docId = generateDocId();
      console.log('生成文档ID:', docId);
      
      const newDocument = {
        id: docId,
        name: name,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 将新文档添加到文档列表中（持久化）
      const documents = loadDocuments()
      documents.push(newDocument);
      const ok = saveDocuments(documents)
      console.log('创建文档成功:', newDocument, '持久化写入：', ok);
      console.log('当前文档列表长度:', documents.length);
      
      ctx.body = {
        code: 200,
        data: newDocument,
        message: '创建文档成功'
      };
    } catch (error) {
      console.error('创建文档失败:', error);
      ctx.body = {
        code: 500,
        message: '创建文档失败',
        error: error.message
      };
    }
  });

  // 删除文档
  router.delete(`${prefix}/documents/:id`, async (ctx) => {
    try {
      const { id } = ctx.params;
      
      // 从文档列表中删除文档（持久化）
      const documents = loadDocuments()
      const index = documents.findIndex(doc => doc.id === id);
      if (index !== -1) {
        documents.splice(index, 1);
        const ok = saveDocuments(documents)
        console.log(`删除文档成功: ${id}，剩余文档数量: ${documents.length}，持久化写入：${ok}`);
        
        ctx.body = {
          code: 200,
          message: '删除文档成功'
        };
      } else {
        ctx.body = {
          code: 404,
          message: '文档不存在'
        };
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '删除文档失败',
        error: error.message
      };
    }
  });

  // 重命名文档
  router.put(`${prefix}/documents/:id`, async (ctx) => {
    try {
      const { id } = ctx.params;
      const { name } = ctx.request.body;
      
      if (!name) {
        ctx.body = {
          code: 400,
          message: '文档名称不能为空'
        };
        return;
      }

      // 在文档列表中找到并更新文档（持久化）
      const documents = loadDocuments()
      const docIndex = documents.findIndex(doc => doc.id === id);
      if (docIndex !== -1) {
        documents[docIndex].name = name;
        documents[docIndex].updatedAt = new Date().toISOString();
        const ok = saveDocuments(documents)
        
        console.log(`重命名文档成功: ${id} -> ${name}，持久化写入：${ok}`);
        
        ctx.body = {
          code: 200,
          data: documents[docIndex],
          message: '重命名文档成功'
        };
      } else {
        ctx.body = {
          code: 404,
          message: '文档不存在'
        };
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '重命名文档失败',
        error: error.message
      };
    }
  });
};

// 生成文档ID的辅助函数
function generateDocId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}