import koa from 'koa';
import staticServer from 'koa-static';
import fs from 'fs';
import koaBody from 'koa-body';
import glob from 'glob';
import config from './config';
import cors from 'koa2-cors';
import Router from '@koa/router';
import http from 'http';
import compress from 'koa-compress';
import koajwt from 'koa-jwt';
import { setupWSConnection } from "y-websocket/bin/utils"
import WebSocket from "ws";
// 存储文档与用户的映射关系
const docClients = new Map(); // 格式: { docName: Set<clientId> }
const clientInfo = new Map(); // 格式: { clientId: { ws, userId, docName } }

const router = new Router()

// 启动逻辑
async function start() {
    const app = new koa();

    const server = http.createServer(app.callback());

    // 开启gzip
    const options = { threshold: 2048 };
    app.use(compress(options));

    // 设置静态目录
    app.use(staticServer(config.publicPath, { maxage: 60 * 60 * 1000 }))
    app.use(staticServer(config.appStaticPath, { maxage: 60 * 60 * 1000 }))
    // 设置跨域
    app.use(cors({
        origin: function (ctx) {
            return '*'
        },
        exposeHeaders: ['WWW-Authenticate', 'Server-Authorization', 'x-show-msg'],
        maxAge: 5,  //  该字段可选，用来指定本次预检请求的有效期，单位为秒
        credentials: true,
        allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-Requested-With'],
    }))

    app.use(koaBody());

    // 健康检查
    app.use(async (ctx, next) => {
        if (ctx.path === '/health') {
            ctx.type = 'application/json'
            ctx.body = { response: 'ok' }
            return true
        }
        await next()
    })

    // jwt验证白名单
    app.use(koajwt({ secret: config.jwt_secret }).unless({
        path: [
            /^\/favicon.ico/, // 网站图标
            /^\/api\/v1\/user\/login/, // 登陆接口
            /^\/api\/v1\/user\/register/, // 注册
            //  sse事件推送
            /^\/api\/v1\/events\/on/, // sse推送
            /^\/api\/v1\/track\/common/, // 上报埋点
            /^\/api\/v1\/track\/add/, // 添加埋点
            /^\/api\/v1\/ai\/tyqw/, // ai接口
            /^\/api\/v1\/ai\/tyqw\/free/, // ai免费接口
            /^\/api\/v1\/ai\/token/, // ai接口
            /^\/api\/v1\/parse\/doc2html/, // 文档解析接口
            /^\/api\/v1\/parse\/doc2html2/, // 文档解析接口
            '/api/v0/parse/doc2html2/:docId/:page', // 文档解析接口
            /^\/api\/v1\/upload\/free/, // 文件上传
            /^\/y-static(\/.*)?$/, // 静态资源
            /^\/api\/v1\/mock\/doc/, // mock数据
            /^\/api\/v1\/documents/, // 文档管理接口
            /^\/api\/v1\/documents\/.*\/versions/, // 版本管理接口
        ]
    }))

    // 挂载路由
    glob.sync(`${config.routerPath}/*.js`).forEach(item => {
        require(item).default(router, config.API_VERSION_PATH)
    })

    app.use(router.routes()).use(router.allowedMethods())

    // yjs协同的socket服务
    const wss = new WebSocket.Server({
        server
    })

    wss.on("connection", async (ws, request) => {
        // 从 URL 参数或请求头获取用户信息
        const url = new URL(request.url, `http://${request.headers.host}`);
        const userId = url.searchParams.get("userId") || generateRandomId();
        // 使用路径作为文档名称，保持与 y-websocket 的房间路由一致
        const pathnameDoc = url.pathname.slice(1);
        const docName = pathnameDoc || url.searchParams.get("spaceId") || 'test';
        const color = url.searchParams.get("color") || '06c';
        const username = url.searchParams.get("username");

        // 存储客户端信息
        const clientId = url.searchParams.get("clientId") || generateRandomId();
        clientInfo.set(clientId, { ws, userId, docName, color, username });

        // 更新文档的用户集合
        if (!docClients.has(docName)) {
            docClients.set(docName, new Set());
        }
        docClients.get(docName).add(clientId);

        // 通知空间内其他用户有新用户加入
        broadcastToDoc(docName, {
            type: "user-joined",
            userId,
            timestamp: Date.now(),
        }, clientId);

        console.log(`用户 ${userId} 连接到文档: ${docName}`);

        // 设置连接断开时的清理逻辑
        ws.on("close", () => {
            // 从文档用户集合中移除
            if (docClients.has(docName)) {
                const clients = docClients.get(docName);
                clients.delete(clientId);

                // 通知空间内其他用户有用户离开
                broadcastToDoc(docName, {
                    type: "user-left",
                    userId,
                    timestamp: Date.now(),
                });

                // 如果文档没有用户了，可以选择释放资源
                if (clients.size === 0) {
                    docClients.delete(docName);
                    // 可选: 释放文档资源或保存到持久化存储
                }
            }

            // 从客户端信息中移除
            clientInfo.delete(clientId);
        });

        // 交给 y-websocket 按房间名(路径)自动管理与隔离文档
        setupWSConnection(ws, request);
    });

    // 广播消息到指定文档的所有客户端
    function broadcastToDoc(docName, message, excludeClientId = null) {
        if (!docClients.has(docName)) return;

        const clients = docClients.get(docName);
        const messageJson = JSON.stringify(message);

        clients.forEach((clientId) => {
            if (clientId !== excludeClientId) {
                const client = clientInfo.get(clientId);
                if (client && client.ws.readyState === WebSocket.OPEN) {
                    client.ws.send(messageJson);
                }
            }
        });
    }

    // 生成随机 ID
    function generateRandomId() {
        return Math.random().toString(36).substring(2, 10);
    }

    // API 端点：获取所有文档的用户统计信息
    function getDocStatistics() {
        return Array.from(docClients.entries()).map(([docName, clients]) => ({
            docName,
            userCount: clients.size,
            userIds: Array.from(clients).map((clientId) =>
                clientInfo.get(clientId).userId
            ),
        }));
    }

    // API 端点：获取特定文档的用户列表
    function getUsersInDoc(docName) {
        if (!docClients.has(docName)) {
            return { docName, userCount: 0, users: [] };
        }

        const clients = docClients.get(docName);
        return {
            docName,
            userCount: clients.size,
            users: Array.from(clients).map((clientId) => {
                const client = clientInfo.get(clientId);
                return { userId: client.userId, color: client.color, username: client.username };
            }),
        };
    }

    // 定期向所有客户端发送用户统计信息
    setInterval(() => {
        clientInfo.forEach((client) => {
            if (client.ws.readyState === WebSocket.OPEN) {
                const docStats = getUsersInDoc(client.docName);
                client.ws.send(JSON.stringify({
                    type: 'user-stats',
                    stats: docStats
                }));
            }
        });
    }, 5000); // 5秒更新一次

    server.listen(config.serverPort, () => {
        console.log(`服务器地址:${config.staticPath}`)
    });
}

start()