{"name": "server", "version": "1.0.0", "description": "", "main": "dist/index.js", "dependencies": {"@alicloud/dysmsapi20170525": "^2.0.24", "@alicloud/openapi-client": "^0.4.6", "@alicloud/tea-util": "^1.4.7", "@koa/multer": "^2.0.2", "@koa/router": "^8.0.8", "adm-zip": "^0.5.16", "axios": "^1.7.7", "cheerio": "^1.0.0", "exceljs": "^4.4.0", "glob": "^7.1.4", "ioredis": "^5.4.1", "jsdom": "^25.0.1", "jsonwebtoken": "^9.0.1", "jszip": "^3.10.1", "koa": "^2.7.0", "koa-body": "^4.1.1", "koa-compress": "^5.0.1", "koa-jwt": "^4.0.4", "koa-logger": "^3.2.1", "koa-router": "^7.4.0", "koa-session": "^5.12.3", "koa-sse-stream": "^0.2.0", "koa-static": "^5.0.0", "koa2-cors": "^2.0.6", "koa2-request": "^1.0.4", "lru-cache": "^11.0.2", "mammoth": "^1.8.0", "mathjax-node": "^2.1.1", "multer": "^1.4.2", "nanoid": "^5.0.7", "nodemailer": "^6.9.15", "pdfjs-dist": "^4.10.38", "ramda": "^0.26.1", "socket.io": "^4.8.1", "unzipper": "^0.12.3", "wechat-api": "^1.35.1", "wkhtmltopdf": "^0.4.0", "ws": "^8.18.1", "xml-js": "^1.6.11", "xml2js": "^0.6.2", "y-leveldb": "^0.2.0", "y-websocket": "^2.1.0", "yjs": "^13.6.23", "zlib": "^1.0.5"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/preset-env": "^7.5.5", "@tsconfig/node16": "^16.1.1", "@types/glob": "^8.1.0", "@types/koa": "^2.14.0", "@types/koa-compress": "^4.0.6", "@types/koa-static": "^4.0.4", "@types/koa2-cors": "^2.0.5", "@types/koa__router": "^12.0.4", "@types/node": "^20.11.16", "nodemon": "^1.19.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "scripts": {"start": "export NODE_ENV=development && nodemon -w src --exec \"babel-node src\"", "start:win": "set NODE_ENV=development&& npx babel-node src", "build": "babel src --out-dir dist", "build:js": "babel src --out-dir dist", "run-build": "node dist", "build:ts": "npx tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "files": ["dist", "src"], "author": "", "license": "ISC"}