import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import {
  ArcoResolver,
  VueUseComponentsResolver,
  VueUseDirectiveResolver
} from 'unplugin-vue-components/resolvers'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import UnoCSS from 'unocss/vite'

const ws_url = process.env.NODE_ENV === 'production' ? 'wss://flowmix.turntip.cn' : `ws://localhost:3002`;
const base_url = process.env.NODE_ENV === 'production' ? 'https://flowmix.turntip.cn/api/v1' : 'http://localhost:3002/api/v1';

export default defineConfig({
  define: {
    "process.env": {
      // base: "/docx-vue/",
      // BASE_API_URL: "https://flowmix.turntip.cn/api/v1",
      BASE_API_URL: base_url,
      BASE_WS_URL: ws_url,
      UPLOAD_PATH: "/upload/free",
    },
  },
  base: '/px-editor/',
  publicDir: '/px-editor/',
  build: {
    outDir: 'px-editor',
  },
  server: {
    host: '0.0.0.0',
    port: '9999',
    open: true,
    strictPort: true,
    cors: true
  },
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
    UnoCSS({
      configFile: './unocss.config.js'
    }),
    AutoImport({
      include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.md$/],
      imports: ['vue', 'pinia', 'vue-router', 'vue-i18n', '@vueuse/core'],
      eslintrc: {
        enabled: true,
        filepath: './.eslintrc-auto-import.json',
        globalsPropValue: true
      },
      resolvers: [ArcoResolver()]
    }),
    Components({
      dirs: ['src/components/', 'src/views/', 'src/layout'],
      include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
      resolvers: [
        ArcoResolver({
          sideEffect: true
        }),
        VueUseComponentsResolver(),
        VueUseDirectiveResolver(),
        IconsResolver({
          prefix: 'icon',
          customCollections: ['px']
        })
      ]
    }),
    Icons({
      compiler: 'vue3',
      customCollections: {
        isle: FileSystemIconLoader('src/assets/svg/isle', svg =>
          svg.replace(/^<svg /, '<svg fill="currentColor" ')
        )
      },
      autoInstall: true
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        sassOptions: {
          outputStyle: 'compressed'
        }
      },
      // less: {
      //   // modifyVars: { '@prefix': 'px' },
      //   javascriptEnabled: true,
      // },
    }
  }
})
