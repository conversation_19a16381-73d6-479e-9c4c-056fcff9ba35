<template>
  <div class="w-full h-100vh flex overflow-hidden">
    <div
      class="w-320px h-full border-r-1 border-r-[var(--color-border-1)] border-r-solid box-border overflow-hidden transition-all duration-300"
      :class="{ 'w-0px!': !showSidebar }"
    >
      <PxEditorToc
        v-if="editorEl?.editor"
        :scrollView="scrollViewRef"
        :editor="editorEl?.editor"
      ></PxEditorToc>
    </div>
    <div class="w-full h-full flex-1 flex flex-col">
      <div
        class="w-full h-52px border-b-1 border-b-[var(--color-border-1)] border-b-solid box-border flex justify-between"
      >
        <div class="h-full flex items-center pl-20px">
          <span class="font-bold mr-10">px-doc 协同文档</span>
          <a-dropdown @select="handleDocumentSelect">
            <span class="flex items-center">
              {{ currentDocumentName }}
              <span v-html="icon['arrow-down'].body" class="ml-2 cursor-pointer"></span>
            </span>
            <template #content>
              <a-doption
                v-for="doc in documentList"
                :key="doc.id"
                :value="doc.id"
              >
                {{ doc.name }}
              </a-doption>
              <a-divider style="margin: 4px 0;" />
              <a-doption value="__create_new__">
                <span v-html="icon['plus'].body" style="vertical-align: -2px;margin-right: 4px"></span>
                新建文档
              </a-doption>
            </template>
          </a-dropdown>
          <a-tooltip content="新建文档">
            <span
              v-html="icon['plus'].body"
              class="ml-2 cursor-pointer hover:text-blue-500 transition-colors"
              @click="handleCreateDocument"
            ></span>
          </a-tooltip>
        </div>

        <div class="h-full flex justify-end items-center">
          <div class="m-r-6">
            <a-avatar-group :size="24" :max-count="8">
              <a-avatar
                v-for="(user, index) in users"
                :key="user.userId || index"
                :style="{ backgroundColor: `#${user.color}` }"
              >
                {{ user.username }}
              </a-avatar>
            </a-avatar-group>
          </div>
          <div class="m-r-6">
            在线人数：{{ users.length }}
          </div>
          <div class="m-r-6">
            <a-button type="primary" @click="handleOpenShareModal">分享</a-button>
          </div>

        </div>
      </div>
      <div
        class="w-full flex-center border-b-1 border-b-[var(--color-border-1)] border-b-solid box-border pt-1 pb-1"
      >
        <a-tooltip content="文档目录">
          <span
            @click="showSidebar = !showSidebar"
            class="cursor-pointer text-[var(--color-text-2)]! m-r-6"
            v-html="icon['toc'].body"
            style=""
          >
        </span>
        </a-tooltip>

        <PxEditorToolbar
          v-if="editorEl?.editor"
          :editor="editorEl?.editor"
          :uploadFn="uploadFn"
        ></PxEditorToolbar>

        <a-tooltip content="版本历史">
          <a-button
            class="flex items-center ml-4"
            size="small"
            @click="handleOpenVersionManager"
          >
            <span v-html="icon['history'].body" class="mr-1"></span>
            版本
          </a-button>
        </a-tooltip>

        <a-dropdown @select="handleExportSelect">
            <a-button class="flex items-center ml-4" size="small">
              导出为
              <span v-html="icon['arrow-down-mini'].body" class="ml-2 cursor-pointer"></span>
            </a-button>
          <template #content>
            <a-doption :value="{ value: 'JSON' }"><span v-html="icon['json'].body" style="vertical-align: -2px;margin-right: 4px"></span> JSON</a-doption>
            <a-doption :value="{ value: 'HTML' }"><span v-html="icon['html'].body" style="vertical-align: -2px;margin-right: 4px"></span> HTML</a-doption>
            <a-doption :value="{ value: 'docx' }"><span v-html="icon['docx'].body" style="vertical-align: -2px;margin-right: 4px"></span> DOCX</a-doption>
          </template>
        </a-dropdown>
      </div>
      <div
        ref="scrollViewRef"
        class="w-full h-full overflow-y-auto overflow-x-hidden flex-1"
      >
        <div class="max-w-42rem w-full mx-auto flex flex-1 flex-col">
          <PxEditorBubble v-if="editorEl?.editor" :editor="editorEl?.editor">
          </PxEditorBubble>
          <PxEditor
            ref="editorEl"
            v-model="content"
            :extensions="extensions"
            @update="editorUpdate"
          ></PxEditor>
        </div>
      </div>
    </div>
    <div class="footer">
      <div
        class="flex justify-center text-0.8rem text-[var(--color-text-3)] font-500"
        v-if="!editorEl?.isEmpty"
      >
        <span class="mr-3">段落数：{{ words }}</span>
        <span>字数：{{ characters }}</span>
      </div>
      <div
        class="flex justify-center items-center text-0.8rem text-[var(--color-text-3)] font-500"
        v-else
      >
        内容为空
      </div>
    </div>
    <!-- 分享文档弹窗 -->
    <a-modal v-model:visible="visible" @ok="handleOk" @cancel="handleCancel">
      <template #title>
        分享文档
      </template>
        <div class="text-center">
          <p>请扫描下方二维码分享文档</p>
          <div class="mt-4">
            <QRCode :value="shareLink" :size="180" />
          </div>
          <p class="mt-4 text-sm text-gray-500">{{ shareLink }}</p>
        </div>
    </a-modal>

    <!-- 新建文档弹窗 -->
    <a-modal
      v-model:visible="createDocVisible"
      @ok="handleCreateDocumentConfirm"
      @cancel="handleCreateDocumentCancel"
      title="新建文档"
      :ok-loading="createDocLoading"
    >
      <a-form :model="createDocForm" layout="vertical">
        <a-form-item label="文档名称" required>
          <a-input
            v-model="createDocForm.name"
            placeholder="请输入文档名称"
            @keyup.enter="handleCreateDocumentConfirm"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 版本管理组件 -->
    <VersionManager
      v-model:visible="versionManagerVisible"
      :document-id="currentDocumentId"
      :current-content="currentEditorContent"
      @restore="handleRestoreVersion"
    />
  </div>
</template>

<script setup>
import {
  NotionKit,
  PxEditor,
  PxEditorBubble,
  PxEditorToolbar,
  PxEditorToc,
  createSlashSuggestion,
} from '@/packages/vue3'

// 协同相关配置
import Collaboration from '@tiptap/extension-collaboration'
import * as Y from 'yjs'
import QRCode from 'qrcode.vue';
import { Message } from '@arco-design/web-vue';
import { onMounted, onUnmounted, computed } from 'vue';
import { WebsocketProvider } from 'y-websocket'
import CollaborationCursor from '@tiptap/extension-collaboration-cursor'
import { ws_url } from '@/utils/tool'
import icon from '@/utils/icon'
import { uploadFn } from '@/utils/upload'
import { documentApi } from '@/api/document'
import VersionManager from '@/components/VersionManager.vue'

const content = ref('<div></div>')
const editorEl = ref(null)
const scrollViewRef = ref(null)
const users = ref([])

const characters = ref(0)
const words = ref(0)

const visible = ref(false)
const shareLink = ref(window.location.href)

// 文档管理相关状态
const documentList = ref([])
const currentDocumentName = ref('测试文档')
const currentDocumentId = ref('test')

// 新建文档弹窗状态
const createDocVisible = ref(false)
const createDocLoading = ref(false)
const createDocForm = ref({
  name: ''
})

// 版本管理相关状态
const versionManagerVisible = ref(false)
const autoSaveTimer = ref(null)
const lastSavedContent = ref(null)
const didAutoRestoreLatest = ref(false)

// 计算属性：获取编辑器当前内容
const currentEditorContent = computed(() => {
  return editorEl.value?.editor?.getJSON() || null
})

function charactersCount(editor) {
  const characterObj = editor.getCharacters()
  characters.value = characterObj.characters
  words.value = characterObj.words
}

function editorUpdate({ editor, output }) {
  console.log(output)
  charactersCount(editor)
}

// 文档选择处理
const handleDocumentSelect = (docId) => {
  if (docId === '__create_new__') {
    handleCreateDocument()
    return
  }

  const selectedDoc = documentList.value.find(doc => doc.id === docId)
  if (selectedDoc) {
    currentDocumentId.value = docId
    currentDocumentName.value = selectedDoc.name
    // 跳转到新文档 - 使用正确的路径
    const newPath = window.location.pathname.replace(/\/[^\/]*$/, `/${docId}`)
    window.location.href = newPath
  }
}

// 创建新文档
const handleCreateDocument = () => {
  createDocForm.value.name = ''
  createDocVisible.value = true
}

// 确认创建文档
const handleCreateDocumentConfirm = async () => {
  if (!createDocForm.value.name || createDocForm.value.name.trim() === '') {
    Message.warning('请输入文档名称')
    return
  }

  createDocLoading.value = true
  try {
    console.log('正在创建文档:', createDocForm.value.name.trim())
    const response = await documentApi.createDocument(createDocForm.value.name.trim())
    console.log('创建文档响应:', response)

    if (response.code === 200) {
      Message.success('创建文档成功')
      createDocVisible.value = false

      // 更新当前文档信息
      currentDocumentId.value = response.data.id
      currentDocumentName.value = response.data.name

      // 重新加载文档列表
      await loadDocumentList()

      // 跳转到新文档 - 使用正确的路径
      const newPath = window.location.pathname.replace(/\/[^\/]*$/, `/${response.data.id}`)
      window.location.href = newPath
    } else {
      Message.error(response.message || '创建文档失败')
    }
  } catch (error) {
    console.error('创建文档失败:', error)
    Message.error(`创建文档失败: ${error.message || '未知错误'}`)
  } finally {
    createDocLoading.value = false
  }
}

// 取消创建文档
const handleCreateDocumentCancel = () => {
  createDocVisible.value = false
  createDocForm.value.name = ''
}

// 更新当前文档名称
const updateCurrentDocumentName = () => {
  const currentDoc = documentList.value.find(doc => doc.id === currentDocumentId.value)
  if (currentDoc) {
    currentDocumentName.value = currentDoc.name
    console.log('更新当前文档名称:', currentDoc.name)
  } else {
    // 如果在列表中找不到当前文档，尝试生成一个友好的名称
    if (currentDocumentId.value && currentDocumentId.value !== 'test') {
      currentDocumentName.value = `文档 ${currentDocumentId.value.substring(0, 8)}...`
    } else {
      currentDocumentName.value = '测试文档'
    }
    console.log('当前文档不在列表中，使用生成的名称:', currentDocumentName.value)
  }
}

// 加载文档列表
const loadDocumentList = async () => {
  try {
    console.log('正在加载文档列表...')
    const response = await documentApi.getDocuments()
    console.log('文档列表响应:', response)

    if (response.code === 200) {
      documentList.value = response.data
      console.log('文档列表加载成功:', response.data)
    } else {
      console.error('加载文档列表失败:', response.message)
    }
  } catch (error) {
    console.error('加载文档列表失败:', error)
  }
}

const handleExportSelect = (v) => {
  console.log(v)
  if(v.value === 'JSON') {
    exportJSON()
    return
  }
  if(v.value === 'HTML') {
    exportHtml()
    return
  }
  if(v.value === 'docx') {
    Message.info('该功能正在迭代中，敬请期待')
  }
}

function exportHtml() {
  const html = editorEl.value?.editor.getHTML();
  const blob = new Blob([html], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `px_${Date.now()}.html`
  a.click()
  URL.revokeObjectURL(url)
}

function exportJSON() {
  const json = editorEl.value?.editor.getJSON();
  const jsonStr = JSON.stringify(json, null, 4);
  const blob = new Blob([jsonStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `px_${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const showSidebar = ref(false)

// 分享弹窗逻辑
const handleOpenShareModal = () => {
  visible.value = true
}

const handleOk = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

// 协同相关
const doc = new Y.Doc();
// 获取或生成用户信息
const userId = localStorage.getItem('uid');
const username = localStorage.getItem('username');
const spaceId = window.location.pathname.split('/').pop() || 'test'; // 从URL获取空间ID，默认为test
const userColor = localStorage.getItem('userColor');
const clientId = localStorage.getItem('uid');

// 设置当前文档信息
currentDocumentId.value = spaceId

// 页面加载时获取文档列表并设置当前文档名称
onMounted(async () => {
  await loadDocumentList()
  // 从文档列表中找到当前文档的名称
  updateCurrentDocumentName()
  // 启动自动保存
  startAutoSave()
})

// 页面卸载时清理自动保存定时器
onUnmounted(() => {
  stopAutoSave()
})

// 添加快捷键支持
onMounted(() => {
  const handleKeyDown = (event) => {
    // Ctrl+S 或 Cmd+S 快速保存版本
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault()
      handleQuickSaveVersion()
    }
    // Ctrl+H 或 Cmd+H 打开版本历史
    if ((event.ctrlKey || event.metaKey) && event.key === 'h') {
      event.preventDefault()
      handleOpenVersionManager()
    }
  }

  document.addEventListener('keydown', handleKeyDown)

  // 清理事件监听器
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
  })
})

// 快速保存版本
const handleQuickSaveVersion = async () => {
  try {
    const { versionApi } = await import('@/api/version')
    const response = await versionApi.createVersion(currentDocumentId.value, {
      content: editorEl.value?.editor.getJSON(),
      title: `快速保存 ${new Date().toLocaleString()}`,
      description: '用户快速保存',
      isAutoSave: false,
      author: localStorage.getItem('username') || 'Anonymous'
    })

    if (response.code === 200) {
      Message.success('版本保存成功 (Ctrl+S)')
    }
  } catch (error) {
    console.error('快速保存失败:', error)
    Message.error('保存失败')
  }
}

// 监听文档列表变化，自动更新当前文档名称
watch(documentList, () => {
  updateCurrentDocumentName()
}, { deep: true })

// 版本管理相关方法
const handleOpenVersionManager = () => {
  versionManagerVisible.value = true
}

const handleRestoreVersion = (versionContent) => {
  // 恢复版本内容到编辑器
  if (editorEl.value?.editor && versionContent) {
    try {
      console.log('开始恢复版本，原始内容:', versionContent)

      // 检查协同编辑状态
      const isCollaborative = provider && provider.ws && provider.ws.readyState === WebSocket.OPEN
      console.log('协同编辑状态:', isCollaborative ? '已连接' : '未连接')

      // 方法1: 使用TipTap的setContent命令（最可靠）
      if (typeof versionContent === 'object' && versionContent.type === 'doc') {
        // 如果是完整的TipTap文档对象
        editorEl.value.editor.commands.setContent(versionContent, false)
      } else if (Array.isArray(versionContent)) {
        // 如果是内容数组
        editorEl.value.editor.commands.setContent({ type: 'doc', content: versionContent }, false)
      } else if (typeof versionContent === 'string') {
        // 如果是HTML字符串
        editorEl.value.editor.commands.setContent(versionContent, true)
      } else {
        // 其他格式，尝试直接设置
        editorEl.value.editor.commands.setContent(versionContent, false)
      }

      // 强制更新编辑器状态
      editorEl.value.editor.commands.focus()

      // 更新content响应式变量
      content.value = editorEl.value.editor.getHTML()

      // 在协同环境中，可能需要等待一下让Yjs同步
      if (isCollaborative) {
        setTimeout(() => {
          console.log('协同环境版本恢复完成，当前内容:', editorEl.value.editor.getJSON())
        }, 100)
      }

      console.log('版本恢复成功，编辑器内容已更新')
      Message.success('版本恢复成功')
    } catch (error) {
      console.error('版本恢复失败:', error)
      Message.error('版本恢复失败: ' + error.message)
    }
  } else {
    console.error('编辑器未初始化或版本内容为空')
    Message.error('版本恢复失败: 编辑器未就绪')
  }
}

// 自动保存功能
const startAutoSave = () => {
  // 每5分钟自动保存一次
  autoSaveTimer.value = setInterval(() => {
    if (editorEl.value?.editor && content.value !== lastSavedContent.value) {
      autoSaveVersion()
    }
  }, 5 * 60 * 1000) // 5分钟
}

const stopAutoSave = () => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
    autoSaveTimer.value = null
  }
}

const autoSaveVersion = async () => {
  try {
    const { versionApi } = await import('@/api/version')
    const response = await versionApi.createVersion(currentDocumentId.value, {
      content: editorEl.value?.editor.getJSON(),
      title: `自动保存 ${new Date().toLocaleString()}`,
      description: '系统自动保存',
      isAutoSave: true,
      author: localStorage.getItem('username') || 'Anonymous'
    })

    if (response.code === 200) {
      lastSavedContent.value = content.value
      console.log('自动保存成功')
    }
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

// 使用标准 y-websocket 连接：将房间名放在路径中，附带额外参数
const provider = new WebsocketProvider(
  ws_url,
  spaceId,
  doc,
  {
    params: {
      userId: userId || '',
      username: username || '',
      color: (userColor || '#06c').slice(1),
      clientId: clientId || ''
    }
  }
);

// 监听连接状态
provider.on('status', (event) => {
  console.log('Connection status:', event.status);
});

// 当与服务器同步完成时，如果文档为空自动恢复最近一个版本
provider.on('synced', async (synced) => {
  try {
    if (!synced || didAutoRestoreLatest.value) return
    // 略等一帧，确保编辑器已挂载
    setTimeout(async () => {
      const isEmpty = isEditorDocEmpty()
      if (!isEmpty) return
      const restored = await autoRestoreLatestIfEmpty()
      if (restored) didAutoRestoreLatest.value = true
    }, 50)
  } catch (e) {
    console.error('自动恢复最近版本失败:', e)
  }
})

// 监听自定义消息
// 保存Yjs默认的消息处理函数
const originalOnMessage = provider.ws.onmessage;

// 添加自定义消息监听
provider.ws.addEventListener('message', (event) => {
  try {
    const data = JSON.parse(event.data);
    switch (data.type) {
      case 'user-stats':
        users.value = data.stats.users;
        break;

      case 'user-joined':
        console.log('User joined:', data);
        break;

      case 'user-left':
        console.log('user-left left', data);
        break;
    }
  } catch (e) {
    if (originalOnMessage && typeof originalOnMessage === 'function') {
      originalOnMessage.call(provider.ws, event);
    }
  }
});

// 注册插件
const extensions = [
  NotionKit.configure({
    commandSlash: createSlashSuggestion(),
    uniqueID: {
      types: ['heading', 'paragraph', 'bulletList', 'orderedList', 'taskList']
    }
  }),
  // Document,
  Collaboration.configure({
    document: doc, // Configure Y.Doc for collaboration
  }),
  CollaborationCursor.configure({
    provider,
    user: {
      name: username,
      color: userColor,
    },
  }),
];

// 判断编辑器文档是否为空（空 doc 或仅空段落）
function isEditorDocEmpty() {
  const editor = editorEl.value?.editor
  if (!editor) return false
  const json = editor.getJSON()
  if (!json || !json.content || json.content.length === 0) return true
  if (json.content.length === 1 && json.content[0]?.type === 'paragraph' && (!json.content[0].content || json.content[0].content.length === 0)) return true
  return false
}

// 若为空则拉取最近一个版本并写入编辑器（协同下使用 setContent 触发同步）
async function autoRestoreLatestIfEmpty() {
  try {
    const { versionApi } = await import('@/api/version')
    const resp = await versionApi.getVersions(currentDocumentId.value, 1, 1)
    if (resp?.code === 200 && Array.isArray(resp.data?.versions) && resp.data.versions.length > 0) {
      const latest = resp.data.versions[0]
      if (latest?.content) {
        handleRestoreVersion(latest.content)
        console.log('已自动恢复最近版本:', latest.id)
        return true
      }
    }
  } catch (e) {
    console.error('获取/恢复最近版本失败:', e)
  }
  return false
}
</script>

<style>
.px-editor.ProseMirror .collaboration-cursor__caret {
  border-right: 1px solid transparent;
  border-left: 1px solid transparent;
  pointer-events: none;
  margin-left: -1px;
  margin-right: -1px;
  position: relative;
  word-break: normal;
}
.px-editor.ProseMirror .collaboration-cursor__label {
  border-radius: 0.25rem;
  border-bottom-left-radius: 0;
  font-size: 0.75rem;
  font-weight: 600;
  left: -1px;
  line-height: 1;
  padding: 0.125rem 0.375rem;
  position: absolute;
  top: -1.3em;
  user-select: none;
  white-space: nowrap;
}

.footer {
  position: fixed;
  bottom: 0;
  width :100%;
  height: 30px;
  background: #f8f8f8;
  border-top: 1px solid #ededed;
  display: flex;
  padding: 0 20px;
  align-items: center;
}
</style>
