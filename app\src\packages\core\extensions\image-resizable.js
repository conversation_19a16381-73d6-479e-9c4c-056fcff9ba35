import Image from "@tiptap/extension-image";
import { Plugin, <PERSON>lug<PERSON><PERSON><PERSON> } from "@tiptap/pm/state";
import { NodeSelection } from "@tiptap/pm/state";
import { prefixClass } from "../utils/prefix.js";

// 图片调整大小插件的键
export const imageResizePluginKey = new PluginKey("imageResize");

// 图片调整大小样式
const imageResizeStyles = `
  .${prefixClass}__image-resizable {
    position: relative;
    display: inline-block;
    max-width: 100%;
  }

  .${prefixClass}__image-resizable img {
    max-width: 100%;
    height: auto;
    border: 2px solid transparent;
    border-radius: 6px;
    transition: border-color 0.2s ease;
  }

  .${prefixClass}__image-resizable:hover img {
    border: 2px solid #0f5ae6;
  }

  .${prefixClass}__image-resizable.selected img {
    border: 2px solid #0f5ae6;
  }

  .${prefixClass}__image-resize-handles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
  }

  .${prefixClass}__image-resize-handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #0f5ae6;
    border: 1px solid #fff;
    border-radius: 2px;
    pointer-events: auto;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .${prefixClass}__image-resizable.selected .${prefixClass}__image-resize-handle,
  .${prefixClass}__image-resizable:hover .${prefixClass}__image-resize-handle {
    opacity: 1;
  }

  .${prefixClass}__image-resize-handle.se {
    bottom: -4px;
    right: -4px;
    cursor: se-resize;
  }

  .${prefixClass}__image-resize-handle.sw {
    bottom: -4px;
    left: -4px;
    cursor: sw-resize;
  }

  .${prefixClass}__image-resize-handle.ne {
    top: -4px;
    right: -4px;
    cursor: ne-resize;
  }

  .${prefixClass}__image-resize-handle.nw {
    top: -4px;
    left: -4px;
    cursor: nw-resize;
  }
`;

// 注入样式
let styleInjected = false;
function injectStyles() {
  if (styleInjected) return;
  
  const style = document.createElement('style');
  style.textContent = imageResizeStyles;
  document.head.appendChild(style);
  styleInjected = true;
}

// 图片调整大小插件
function imageResizePlugin() {
  let isResizing = false;
  let resizeStartData = null;

  return new Plugin({
    key: imageResizePluginKey,
    
    props: {
      handleDOMEvents: {
        mousedown(view, event) {
          const target = event.target;
          
          // 检查是否点击了调整手柄
          if (target.classList.contains(`${prefixClass}__image-resize-handle`)) {
            event.preventDefault();
            event.stopPropagation();
            
            const position = target.getAttribute('data-position');
            const imageWrapper = target.closest(`.${prefixClass}__image-resizable`);
            const img = imageWrapper.querySelector('img');
            
            if (!img) return false;
            
            // 查找对应的编辑器节点位置
            let nodePos = null;
            const { state } = view;
            state.doc.descendants((node, pos) => {
              if (node.type.name === 'image') {
                const dom = view.nodeDOM(pos);
                if (dom && (dom === imageWrapper || dom.contains(imageWrapper))) {
                  nodePos = pos;
                  return false; // 停止遍历
                }
              }
            });
            
            if (nodePos === null) return false;
            
            isResizing = true;
            resizeStartData = {
              startX: event.clientX,
              startY: event.clientY,
              startWidth: img.offsetWidth,
              startHeight: img.offsetHeight,
              position,
              img,
              imageWrapper,
              nodePos
            };
            
            // 添加全局鼠标事件监听
            const handleMouseMove = (e) => {
              if (!isResizing || !resizeStartData) return;
              
              const deltaX = e.clientX - resizeStartData.startX;
              const deltaY = e.clientY - resizeStartData.startY;
              
              let newWidth = resizeStartData.startWidth;
              
              // 根据手柄位置计算新尺寸
              switch (resizeStartData.position) {
                case 'se':
                  newWidth += deltaX;
                  break;
                case 'sw':
                  newWidth -= deltaX;
                  break;
                case 'ne':
                  newWidth += deltaX;
                  break;
                case 'nw':
                  newWidth -= deltaX;
                  break;
              }
              
              // 保持最小宽度
              newWidth = Math.max(50, newWidth);
              
              // 保持宽高比
              const aspectRatio = resizeStartData.startHeight / resizeStartData.startWidth;
              const newHeight = newWidth * aspectRatio;
              
              // 应用新尺寸
              resizeStartData.img.style.width = `${newWidth}px`;
              resizeStartData.img.style.height = `${newHeight}px`;
            };
            
            const handleMouseUp = () => {
              if (!isResizing || !resizeStartData) return;
              
              // 更新编辑器中的图片属性
              const { state, dispatch } = view;
              const node = state.doc.nodeAt(resizeStartData.nodePos);
              
              if (node && node.type.name === 'image') {
                const newAttrs = {
                  ...node.attrs,
                  width: Math.round(resizeStartData.img.offsetWidth),
                  height: Math.round(resizeStartData.img.offsetHeight)
                };
                
                const tr = state.tr.setNodeMarkup(
                  resizeStartData.nodePos,
                  null,
                  newAttrs
                );
                
                dispatch(tr);
              }
              
              // 清理
              isResizing = false;
              resizeStartData = null;
              document.removeEventListener('mousemove', handleMouseMove);
              document.removeEventListener('mouseup', handleMouseUp);
            };
            
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
            
            return true;
          }
          
          return false;
        },
        
        click(view, event) {
          const target = event.target;
          
          // 如果点击的是图片，选中它
          if (target.tagName === 'IMG') {
            const imageWrapper = target.closest(`.${prefixClass}__image-resizable`);
            if (imageWrapper) {
              // 查找对应的编辑器节点位置
              const { state, dispatch } = view;
              state.doc.descendants((node, pos) => {
                if (node.type.name === 'image') {
                  const dom = view.nodeDOM(pos);
                  if (dom && (dom === imageWrapper || dom.contains(imageWrapper))) {
                    // 选中这个图片节点
                    const selection = new NodeSelection(state.doc.resolve(pos));
                    dispatch(state.tr.setSelection(selection));
                    return false; // 停止遍历
                  }
                }
              });
            }
          }
          
          return false;
        }
      }
    }
  });
}

export default Image.extend({
  name: 'image', // 保持原有名称
  
  addAttributes() {
    return {
      ...this.parent?.(),
      width: {
        default: null,
        parseHTML: element => {
          const width = element.getAttribute('width') || element.style.width;
          return width ? parseInt(width) : null;
        },
        renderHTML: attributes => {
          if (!attributes.width) return {};
          return { width: attributes.width, style: `width: ${attributes.width}px` };
        }
      },
      height: {
        default: null,
        parseHTML: element => {
          const height = element.getAttribute('height') || element.style.height;
          return height ? parseInt(height) : null;
        },
        renderHTML: attributes => {
          if (!attributes.height) return {};
          return { height: attributes.height, style: `height: ${attributes.height}px` };
        }
      }
    };
  },

  addOptions() {
    return {
      ...this.parent?.(),
      name: "image",
      desc: "",
      command: ({ editor, url }) => {
        if (url) {
          // 记录当前选区位置，用于插入后定位
          const { to } = editor.state.selection;

          // 执行插入图片命令
          editor.chain().focus().setImage({ src: url }).run();

          // 在图片后插入空白文本段落
          editor.chain()
            .focus(to + 1) // 定位到图片后面
            .insertContent([
              {
                type: 'paragraph',
                content: [
                  {
                    type: 'text',
                    text: '', // 空白文本
                  },
                ],
              },
            ])
            .run();
        }
      },
      isActive: ({ editor }) => editor.isActive("image"),
      shortcutkeys: "Mod-M",
      HTMLAttributes: {
        class: `${prefixClass}__image`,
      },
      parseHTML() {
        return [{ tag: 'img' }];
      },
    };
  },

  addProseMirrorPlugins() {
    return [imageResizePlugin()];
  },

  onBeforeCreate() {
    injectStyles();
  },

  addNodeView() {
    return ({ node, HTMLAttributes, getPos }) => {
      const wrapper = document.createElement('div');
      wrapper.className = `${prefixClass}__image-resizable`;
      
      const img = document.createElement('img');
      
      // 设置基本属性
      Object.keys(HTMLAttributes).forEach(key => {
        if (key !== 'width' && key !== 'height' && key !== 'style') {
          img.setAttribute(key, HTMLAttributes[key]);
        }
      });
      
      // 设置尺寸
      if (node.attrs.width) {
        img.style.width = `${node.attrs.width}px`;
      }
      if (node.attrs.height) {
        img.style.height = `${node.attrs.height}px`;
      }
      
      // 添加拖拽句柄
      const handles = document.createElement('div');
      handles.className = `${prefixClass}__image-resize-handles`;
      
      const handlePositions = ['nw', 'ne', 'sw', 'se'];
      handlePositions.forEach(position => {
        const handle = document.createElement('div');
        handle.className = `${prefixClass}__image-resize-handle ${position}`;
        handle.setAttribute('data-position', position);
        handles.appendChild(handle);
      });
      
      wrapper.appendChild(img);
      wrapper.appendChild(handles);
      
      return {
        dom: wrapper,
        contentDOM: null,
        update: (updatedNode) => {
          if (updatedNode.type !== node.type) {
            return false;
          }
          // 更新尺寸
          if (updatedNode.attrs.width) {
            img.style.width = `${updatedNode.attrs.width}px`;
          }
          if (updatedNode.attrs.height) {
            img.style.height = `${updatedNode.attrs.height}px`;
          }
          node = updatedNode;
          return true;
        }
      };
    };
  }
});