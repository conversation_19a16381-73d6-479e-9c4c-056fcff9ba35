import{Y as c,Z as p,V as f,S as C,U as _,B as h}from"./tool-DhuguiQG.js";import{_ as g,C as b,f as v,h as w,j as o,l as a,v as x,e as y,z as S,Y as L}from"./index-BrKIBfdb.js";const k={setup(){const s=L();return{form:b({name:""}),handleSubmit:async e=>{if(e&&e.values){const l=await c();localStorage.setItem("uid",l),localStorage.setItem("username",e.values.name),localStorage.setItem("userColor",p()),s.push("/test")}}}}},V={class:"flex items-center w-full flex-col login-bg"};function B(s,t,i,e,l,I){const r=C,n=_,d=h,m=f;return y(),v("div",V,[t[2]||(t[2]=w("div",{class:"px-login-title"},"px-doc 协同文档编辑器",-1)),o(m,{model:e.form,style:{width:"426px",position:"relative",zIndex:2},onSubmit:e.handleSubmit},{default:a(()=>[o(n,{field:"name"},{default:a(()=>[o(r,{modelValue:e.form.name,"onUpdate:modelValue":t[0]||(t[0]=u=>e.form.name=u),placeholder:"请输入用户名",required:""},null,8,["modelValue"])]),_:1}),o(n,null,{default:a(()=>[o(d,{"html-type":"submit",type:"primary",long:""},{default:a(()=>t[1]||(t[1]=[S("登录/注册")])),_:1})]),_:1})]),_:1},8,["model","onSubmit"]),t[3]||(t[3]=x('<div class="login-bg-img"><svg width="100%" height="100%" id="svg" viewBox="0 0 1440 490" xmlns="http://www.w3.org/2000/svg" class="transition duration-300 ease-in-out delay-150"><path d="M 0,500 L 0,93 C 82.76923076923075,87.64871794871794 165.5384615384615,82.29743589743589 242,82 C 318.4615384615385,81.70256410256411 388.61538461538464,86.45897435897436 477,91 C 565.3846153846154,95.54102564102564 672.0000000000001,99.86666666666667 741,90 C 809.9999999999999,80.13333333333333 841.3846153846154,56.07435897435897 917,62 C 992.6153846153846,67.92564102564103 1112.4615384615386,103.83589743589744 1207,114 C 1301.5384615384614,124.16410256410256 1370.7692307692307,108.58205128205128 1440,93 L 1440,500 L 0,500 Z" stroke="none" stroke-width="0" fill="#abb8c3" fill-opacity="0.4" class="transition-all duration-300 ease-in-out delay-150 path-0"></path><path d="M 0,500 L 0,218 C 98.04102564102564,236.85897435897436 196.08205128205128,255.71794871794873 278,250 C 359.9179487179487,244.28205128205127 425.7128205128206,213.98717948717945 496,207 C 566.2871794871794,200.01282051282055 641.0666666666667,216.33333333333334 724,218 C 806.9333333333333,219.66666666666666 898.0205128205125,206.67948717948715 978,202 C 1057.9794871794875,197.32051282051285 1126.8512820512822,200.94871794871796 1202,205 C 1277.1487179487178,209.05128205128204 1358.5743589743588,213.52564102564102 1440,218 L 1440,500 L 0,500 Z" stroke="none" stroke-width="0" fill="#abb8c3" fill-opacity="0.53" class="transition-all duration-300 ease-in-out delay-150 path-1"></path><path d="M 0,500 L 0,343 C 63.012820512820525,320.8820512820513 126.02564102564105,298.76410256410253 216,311 C 305.97435897435895,323.23589743589747 422.91025641025635,369.82564102564106 505,380 C 587.0897435897436,390.17435897435894 634.3333333333334,363.93333333333334 710,360 C 785.6666666666666,356.06666666666666 889.7564102564104,374.44102564102565 972,373 C 1054.2435897435896,371.55897435897435 1114.6410256410256,350.3025641025641 1189,342 C 1263.3589743589744,333.6974358974359 1351.6794871794873,338.348717948718 1440,343 L 1440,500 L 0,500 Z" stroke="none" stroke-width="0" fill="#abb8c3" fill-opacity="1" class="transition-all duration-300 ease-in-out delay-150 path-2"></path></svg></div>',1))])}const M=g(k,[["render",B]]);export{M as default};
