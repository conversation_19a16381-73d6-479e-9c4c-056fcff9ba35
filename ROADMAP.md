# Roadmap

本清单跟踪 px-doc 的待开发事项与优先级。实际执行顺序会根据需求与缺陷调整。

## 高优先级

1) 协同场景下“版本恢复”升级为 Y.Doc 事务
- 方案：`doc.transact(() => editor.commands.setContent(json, false), 'restore-version')`
- 目标：原子更新、强一致广播、撤销策略不被碎片化污染
- 风险：与大并发输入冲突时的体验，必要时短暂断开/重连或提供服务端恢复接口

2) 版本对比
- 短期：前端文本/DOM diff 高亮
- 中期：基于 ProseMirror Node 的结构化 diff

## 中优先级

3) 版本预览样式优化
- 引入与编辑态一致的 CSS，使预览视觉与编辑保持一致

4) 数据库存储（可选，替代文件方案）
- 迁移 `documents.json` 与 `version/<docId>.json` 至 SQLite/Postgres/MySQL
- 提升并发一致性、查询统计能力；提供查询接口（按作者/时间范围/关键词等）

## 低优先级 / 选做

5) 服务端“恢复版本”接口
- 由服务器替换目标文档的 Y.Doc 状态并广播
- 适用于合规/审计/权限管控更严格的场景

6) 运营与监控
- 指标：版本保存/恢复成功率、响应时间、错误率、协同连接数
- 看板与告警：Grafana/Prometheus/Sentry（按需）

---

如需调整优先级或增加任务，请在 PR 中更新本文件。

