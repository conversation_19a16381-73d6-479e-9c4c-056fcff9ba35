# Design Document

## Overview

This design document outlines the solution for fixing the document isolation issue in the collaborative document editor. The current system treats all connections as editing the same document, regardless of the document ID. We need to implement proper document isolation where each document ID corresponds to a unique Yjs document instance with its own collaborative session.

## Architecture

### Current Architecture Issues

1. **Single Document Instance**: All WebSocket connections share the same Yjs document
2. **No Document ID Routing**: The system doesn't properly extract or use document IDs from connection parameters
3. **Shared User State**: All users appear to be editing the same document regardless of their intended document
4. **No Persistence Strategy**: Documents are not saved or restored properly

### Proposed Architecture

```
Client Request (with docId) 
    ↓
WebSocket Connection Handler
    ↓
Document ID Extraction & Validation
    ↓
Document Manager (creates/retrieves Yjs doc)
    ↓
User Session Management (per document)
    ↓
Real-time Collaboration (isolated per document)
```

## Components and Interfaces

### 1. Document Manager

**Purpose**: Manages the lifecycle of Yjs documents and ensures proper isolation.

**Interface**:
```javascript
class DocumentManager {
  // Get or create a document instance
  getDocument(docId: string): Y.Doc
  
  // Remove document when no users are connected
  cleanupDocument(docId: string): void
  
  // Get all active documents
  getActiveDocuments(): Map<string, Y.Doc>
  
  // Save document to persistent storage
  saveDocument(docId: string, doc: Y.Doc): Promise<void>
  
  // Load document from persistent storage
  loadDocument(docId: string): Promise<Y.Doc>
}
```

### 2. Connection Handler

**Purpose**: Handles WebSocket connections and routes them to the correct document.

**Interface**:
```javascript
class ConnectionHandler {
  // Handle new WebSocket connection
  handleConnection(ws: WebSocket, request: IncomingMessage): void
  
  // Extract document ID from connection parameters
  extractDocumentId(request: IncomingMessage): string
  
  // Validate document ID format
  validateDocumentId(docId: string): boolean
  
  // Setup Yjs connection for specific document
  setupYjsConnection(ws: WebSocket, docId: string, userId: string): void
}
```

### 3. User Session Manager

**Purpose**: Manages user presence and sessions per document.

**Interface**:
```javascript
class UserSessionManager {
  // Add user to document session
  addUserToDocument(docId: string, userId: string, clientInfo: ClientInfo): void
  
  // Remove user from document session
  removeUserFromDocument(docId: string, userId: string): void
  
  // Get users in specific document
  getUsersInDocument(docId: string): UserInfo[]
  
  // Broadcast message to users in document
  broadcastToDocument(docId: string, message: any, excludeUserId?: string): void
}
```

### 4. Persistence Layer

**Purpose**: Handles document persistence and recovery.

**Interface**:
```javascript
class PersistenceLayer {
  // Save document state
  saveDocumentState(docId: string, state: Uint8Array): Promise<void>
  
  // Load document state
  loadDocumentState(docId: string): Promise<Uint8Array | null>
  
  // Delete document
  deleteDocument(docId: string): Promise<void>
  
  // List all documents
  listDocuments(): Promise<string[]>
}
```

## Data Models

### Document Metadata
```javascript
interface DocumentMetadata {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  lastAccessedAt: Date;
  userCount: number;
  size: number;
}
```

### User Session
```javascript
interface UserSession {
  userId: string;
  clientId: string;
  username?: string;
  color: string;
  cursor?: CursorPosition;
  connectedAt: Date;
  lastActiveAt: Date;
}
```

### Client Connection Info
```javascript
interface ClientInfo {
  ws: WebSocket;
  userId: string;
  docId: string;
  clientId: string;
  username?: string;
  color: string;
}
```

## Error Handling

### Connection Errors
- **Invalid Document ID**: Return error message and close connection
- **Document Not Found**: Create new document or return appropriate error
- **Authentication Failure**: Close connection with auth error
- **Network Issues**: Implement reconnection logic with exponential backoff

### Document Errors
- **Document Creation Failure**: Log error and return fallback document
- **Persistence Failure**: Continue with in-memory operation, log error
- **Corruption Detection**: Attempt recovery or create new document

### User Session Errors
- **Duplicate User**: Handle gracefully by updating existing session
- **Session Timeout**: Clean up resources and notify other users
- **Broadcast Failure**: Retry with individual sends, log failures

## Testing Strategy

### Unit Tests
- Document Manager: Test document creation, retrieval, and cleanup
- Connection Handler: Test document ID extraction and validation
- User Session Manager: Test user addition, removal, and broadcasting
- Persistence Layer: Test save/load operations with various scenarios

### Integration Tests
- End-to-end document isolation: Multiple users in different documents
- WebSocket connection flow: From connection to document assignment
- Persistence integration: Document save/restore across server restarts
- Error scenarios: Invalid IDs, network failures, corruption handling

### Performance Tests
- Memory usage with multiple documents
- Connection handling under load
- Document cleanup efficiency
- Persistence operation performance

## Security Considerations

### Document Access Control
- Validate document ID format to prevent path traversal
- Implement document-level permissions if needed
- Rate limiting on document creation
- Input sanitization for user-provided data

### WebSocket Security
- Validate all incoming messages
- Implement connection limits per IP
- Secure document ID transmission
- Prevent document enumeration attacks

## Migration Strategy

### Phase 1: Core Infrastructure
1. Implement DocumentManager class
2. Update WebSocket connection handling
3. Add document ID extraction logic
4. Basic user session management per document

### Phase 2: Persistence Integration
1. Implement PersistenceLayer with file-based storage
2. Add document save/load functionality
3. Implement cleanup mechanisms
4. Add document metadata tracking

### Phase 3: Enhanced Features
1. Document versioning support
2. Advanced user presence features
3. Performance optimizations
4. Monitoring and analytics

### Backward Compatibility
- Maintain existing API endpoints
- Support legacy connection methods during transition
- Gradual migration of existing documents
- Fallback mechanisms for edge cases