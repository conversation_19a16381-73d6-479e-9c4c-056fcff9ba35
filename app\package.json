{"name": "playground", "version": "0.0.11", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@floating-ui/dom": "^1.6.12", "@iconify/vue": "^4.2.0", "@tiptap/core": "^2.26.1", "@tiptap/extension-bold": "^2.9.1", "@tiptap/extension-bubble-menu": "^2.9.1", "@tiptap/extension-bullet-list": "^2.9.1", "@tiptap/extension-character-count": "^2.9.1", "@tiptap/extension-code": "^2.9.1", "@tiptap/extension-code-block": "^2.10.3", "@tiptap/extension-collaboration": "^2.22.3", "@tiptap/extension-collaboration-cursor": "^2.22.3", "@tiptap/extension-document": "^2.10.3", "@tiptap/extension-dropcursor": "^2.9.1", "@tiptap/extension-gapcursor": "^2.9.1", "@tiptap/extension-image": "^2.22.3", "@tiptap/extension-italic": "^2.9.1", "@tiptap/extension-link": "^2.9.1", "@tiptap/extension-list-item": "^2.9.1", "@tiptap/extension-ordered-list": "^2.9.1", "@tiptap/extension-placeholder": "^2.9.1", "@tiptap/extension-strike": "^2.9.1", "@tiptap/extension-subscript": "^2.9.1", "@tiptap/extension-superscript": "^2.9.1", "@tiptap/extension-table": "^2.10.3", "@tiptap/extension-table-cell": "^2.10.3", "@tiptap/extension-table-header": "^2.10.3", "@tiptap/extension-table-row": "^2.10.3", "@tiptap/extension-task-item": "^2.9.1", "@tiptap/extension-task-list": "^2.9.1", "@tiptap/extension-text": "^2.9.1", "@tiptap/extension-typography": "^2.9.1", "@tiptap/extension-underline": "^2.9.1", "@tiptap/pm": "^2.9.1", "@tiptap/suggestion": "^2.9.1", "@vueuse/core": "^11.2.0", "axios": "^1.10.0", "es-drager": "^1.3.0", "i18next": "^23.16.5", "lodash": "^4.17.21", "pinia": "^2.2.4", "prosemirror-state": "^1.4.3", "qrcode.vue": "^3.6.0", "shiki": "^1.24.0", "tippy.js": "^6.3.7", "uuid": "^11.0.2", "vue": "^3.5.12", "vue-i18n": "^10.0.4", "vue-router": "^4.4.5", "y-websocket": "^3.0.0", "yjs": "^13.6.27", "echarts": "5.5.1"}, "devDependencies": {"@arco-design/web-vue": "^2.56.3", "@eslint/js": "^9.13.0", "@iconify-json/lucide": "^1.2.14", "@iconify-json/tabler": "^1.2.10", "@unocss/transformer-directives": "^0.63.6", "@unocss/transformer-variant-group": "^0.63.6", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/eslint-config-prettier": "^10.0.0", "eslint": "^9.13.0", "eslint-plugin-vue": "^9.29.0", "prettier": "^3.3.3", "sass": "^1.80.6", "unocss": "^0.63.6", "unplugin-auto-import": "^0.18.3", "unplugin-icons": "^0.20.0", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.10", "vite-plugin-vue-devtools": "^7.5.3"}, "pnpm": {"overrides": {"@tiptap/extension-mention": "npm:@tiptap/core@^2.26.1", "@tiptap/vue-3": "npm:@tiptap/core@^2.26.1", "echarts": "5.5.1"}}}