// 版本管理路由（文件持久化：server/db/version/<docId>.json）
import fs from 'fs'
import { resolve, dirname } from 'path'

const VERSIONS_DIR = resolve(__dirname, '../../db/version')

function ensureDirExists(path) {
  try { fs.mkdirSync(path, { recursive: true }) } catch (e) {}
}

function getVersionFile(docId) {
  ensureDirExists(VERSIONS_DIR)
  return resolve(VERSIONS_DIR, `${docId}.json`)
}

function loadVersionList(docId) {
  const file = getVersionFile(docId)
  try {
    if (!fs.existsSync(file)) {
      fs.writeFileSync(file, JSON.stringify([], null, 2), 'utf-8')
      return []
    }
    const raw = fs.readFileSync(file, 'utf-8')
    const data = JSON.parse(raw)
    return Array.isArray(data) ? data : []
  } catch (e) {
    console.error(`读取版本文件失败: ${file}`, e)
    return []
  }
}

function saveVersionList(docId, list) {
  const file = getVersionFile(docId)
  try {
    fs.writeFileSync(file, JSON.stringify(list, null, 2), 'utf-8')
    return true
  } catch (e) {
    console.error(`保存版本文件失败: ${file}`, e)
    return false
  }
}

export default (router, prefix) => {
  // 确保目录存在
  ensureDirExists(VERSIONS_DIR)
  // 获取文档的版本列表
  router.get(`${prefix}/documents/:docId/versions`, async (ctx) => {
    try {
      const { docId } = ctx.params;
      const { page = 1, limit = 20 } = ctx.query;
      
      console.log(`获取文档 ${docId} 的版本列表`);
      const all = loadVersionList(docId)
      const docVersions = all.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      
      // 分页处理
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + parseInt(limit);
      const paginatedVersions = docVersions.slice(startIndex, endIndex);
      
      ctx.body = {
        code: 200,
        data: {
          versions: paginatedVersions,
          total: docVersions.length,
          page: parseInt(page),
          limit: parseInt(limit)
        },
        message: '获取版本列表成功'
      };
    } catch (error) {
      console.error('获取版本列表失败:', error);
      ctx.body = {
        code: 500,
        message: '获取版本列表失败',
        error: error.message
      };
    }
  });

  // 创建新版本
  router.post(`${prefix}/documents/:docId/versions`, async (ctx) => {
    try {
      const { docId } = ctx.params;
      const { content, title, description, isAutoSave = false } = ctx.request.body;
      
      if (!content) {
        ctx.body = {
          code: 400,
          message: '版本内容不能为空'
        };
        return;
      }

      // 生成版本ID
      const versionId = generateVersionId();
      const now = new Date().toISOString();
      
      const newVersion = {
        id: versionId,
        documentId: docId,
        content: content,
        title: title || `版本 ${versionId.substring(0, 8)}`,
        description: description || (isAutoSave ? '自动保存' : '手动保存'),
        isAutoSave: isAutoSave,
        createdAt: now,
        updatedAt: now,
        size: JSON.stringify(content).length,
        author: ctx.request.body.author || 'Anonymous'
      };

      // 添加到版本列表（持久化）
      const all = loadVersionList(docId)
      all.push(newVersion)
      const ok = saveVersionList(docId, all)
      
      console.log(`创建版本成功: ${docId} -> ${versionId} 持久化: ${ok}`);
      console.log(`当前版本总数: ${all.length}`);
      
      ctx.body = {
        code: 200,
        data: newVersion,
        message: '创建版本成功'
      };
    } catch (error) {
      console.error('创建版本失败:', error);
      ctx.body = {
        code: 500,
        message: '创建版本失败',
        error: error.message
      };
    }
  });

  // 获取指定版本的详细内容
  router.get(`${prefix}/documents/:docId/versions/:versionId`, async (ctx) => {
    try {
      const { docId, versionId } = ctx.params;
      
      const all = loadVersionList(docId)
      const version = all.find(v => v.id === versionId);
      
      if (!version) {
        ctx.body = {
          code: 404,
          message: '版本不存在'
        };
        return;
      }
      
      console.log(`获取版本详情: ${docId} -> ${versionId}`);
      
      ctx.body = {
        code: 200,
        data: version,
        message: '获取版本详情成功'
      };
    } catch (error) {
      console.error('获取版本详情失败:', error);
      ctx.body = {
        code: 500,
        message: '获取版本详情失败',
        error: error.message
      };
    }
  });

  // 删除版本
  router.delete(`${prefix}/documents/:docId/versions/:versionId`, async (ctx) => {
    try {
      const { docId, versionId } = ctx.params;
      
      const all = loadVersionList(docId)
      const index = all.findIndex(v => v.id === versionId);
      
      if (index === -1) {
        ctx.body = {
          code: 404,
          message: '版本不存在'
        };
        return;
      }
      
      // 不允许删除自动保存的版本（保护数据）
      if (all[index].isAutoSave) {
        ctx.body = {
          code: 403,
          message: '不能删除自动保存的版本'
        };
        return;
      }
      
      all.splice(index, 1);
      const ok = saveVersionList(docId, all)
      console.log(`删除版本成功: ${docId} -> ${versionId}`);
      
      ctx.body = {
        code: 200,
        message: '删除版本成功'
      };
    } catch (error) {
      console.error('删除版本失败:', error);
      ctx.body = {
        code: 500,
        message: '删除版本失败',
        error: error.message
      };
    }
  });

  // 更新版本信息（标题、描述等）
  router.put(`${prefix}/documents/:docId/versions/:versionId`, async (ctx) => {
    try {
      const { docId, versionId } = ctx.params;
      const { title, description } = ctx.request.body;
      
      const all = loadVersionList(docId)
      const versionIndex = all.findIndex(v => v.id === versionId);
      
      if (versionIndex === -1) {
        ctx.body = {
          code: 404,
          message: '版本不存在'
        };
        return;
      }
      
      // 更新版本信息
      if (title !== undefined) all[versionIndex].title = title;
      if (description !== undefined) all[versionIndex].description = description;
      all[versionIndex].updatedAt = new Date().toISOString();
      const ok = saveVersionList(docId, all)
      
      console.log(`更新版本信息成功: ${docId} -> ${versionId}`);
      
      ctx.body = {
        code: 200,
        data: all[versionIndex],
        message: '更新版本信息成功'
      };
    } catch (error) {
      console.error('更新版本信息失败:', error);
      ctx.body = {
        code: 500,
        message: '更新版本信息失败',
        error: error.message
      };
    }
  });

  // 版本对比
  router.get(`${prefix}/documents/:docId/versions/:versionId1/compare/:versionId2`, async (ctx) => {
    try {
      const { docId, versionId1, versionId2 } = ctx.params;
      
      const all = loadVersionList(docId)
      const version1 = all.find(v => v.id === versionId1);
      const version2 = all.find(v => v.id === versionId2);
      
      if (!version1 || !version2) {
        ctx.body = {
          code: 404,
          message: '版本不存在'
        };
        return;
      }
      
      console.log(`版本对比: ${versionId1} vs ${versionId2}`);
      
      ctx.body = {
        code: 200,
        data: {
          version1: {
            id: version1.id,
            title: version1.title,
            createdAt: version1.createdAt,
            content: version1.content
          },
          version2: {
            id: version2.id,
            title: version2.title,
            createdAt: version2.createdAt,
            content: version2.content
          }
        },
        message: '获取版本对比数据成功'
      };
    } catch (error) {
      console.error('版本对比失败:', error);
      ctx.body = {
        code: 500,
        message: '版本对比失败',
        error: error.message
      };
    }
  });
};

// 生成版本ID的辅助函数
function generateVersionId() {
  return 'v_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
}