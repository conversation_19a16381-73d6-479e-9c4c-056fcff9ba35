# 创建文档问题调试指南

## 可能的问题和解决方案

### 1. 路径跳转问题 ✅ 已修复
**问题**: 创建文档后跳转路径不正确
**修复**: 
- 从 `window.location.href = \`/${docId}\`` 
- 改为 `window.location.href = \`/px-editor/${docId}\``

### 2. 网络请求问题
**检查步骤**:
1. 打开浏览器开发者工具 (F12)
2. 查看 Network 标签页
3. 尝试创建文档，观察请求状态

**可能的错误**:
- `CORS error`: 跨域问题
- `404 Not Found`: API路径错误
- `500 Internal Server Error`: 服务器错误
- `Network Error`: 网络连接问题

### 3. 前端错误
**检查步骤**:
1. 打开浏览器开发者工具 Console 标签页
2. 查看是否有JavaScript错误
3. 观察创建文档时的日志输出

### 4. 服务器错误
**检查步骤**:
1. 查看服务器终端输出
2. 观察是否有错误日志
3. 确认API请求是否到达服务器

## 调试命令

### 测试API连接
```bash
# 测试获取文档列表
curl -X GET http://*************:3002/api/v1/documents

# 测试创建文档
curl -X POST http://*************:3002/api/v1/documents \
  -H "Content-Type: application/json" \
  -d '{"name":"测试文档"}'
```

### 检查服务状态
```bash
# 检查端口占用
lsof -i :3002
lsof -i :9999

# 检查进程
ps aux | grep node
ps aux | grep vite
```

## 常见解决方案

### 重启服务
```bash
# 重启后端
cd server && pnpm start

# 重启前端
cd app && pnpm dev
```

### 清理缓存
```bash
# 清理浏览器缓存
# 或使用无痕模式测试

# 清理npm缓存
npm cache clean --force
```

## 预期的正常流程

1. 用户点击"新建文档"按钮
2. 弹出创建文档对话框
3. 用户输入文档名称
4. 点击确认按钮
5. 前端发送POST请求到 `/api/v1/documents`
6. 服务器返回新文档信息
7. 前端显示成功消息
8. 页面跳转到新文档编辑页面

## 如果问题仍然存在

请提供以下信息：
1. 浏览器控制台的错误信息
2. 网络请求的详细信息
3. 服务器终端的输出
4. 具体的错误截图或描述