import { defineComponent, ref, reactive, h, watch, onMounted, nextTick } from "vue";
import { prefixClass } from "../../../core";
import { IButton, IIcon } from "../ui";

export default defineComponent({
  name: "ChartEditor",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    chartData: {
      type: Object,
      default: () => ({
        type: "bar",
        title: "新建图表",
        data: {
          categories: ["类别1", "类别2", "类别3", "类别4"],
          series: [
            {
              name: "系列1",
              data: [4.3, 2.5, 4.5, 5.0]
            }
          ]
        },
        config: {
          theme: "default",
          showLegend: true,
          legendPosition: "bottom",
          legendVerticalPosition: "middle",
          showGrid: true,
          showTooltip: true
        }
      })
    }
  },
  emits: ['update:visible', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const editMode = ref('basic'); // basic | advanced
    const previewContainer = ref(null);
    const chartInstance = ref(null);
    
    // 响应式数据
    const formData = reactive({
      type: 'bar',
      title: '新建图表',
      data: {
        categories: ['类别1', '类别2', '类别3'],
        series: [
          {
            name: '系列1',
            data: [10, 20, 30]
          }
        ]
      },
      config: {
        theme: 'default',
        showLegend: true,
        legendPosition: 'bottom',
        legendVerticalPosition: 'middle',
        showGrid: true,
        showTooltip: true
      }
    });

    // 图表类型选项
    const chartTypes = [
      { value: 'bar', label: '柱状图' },
      { value: 'line', label: '折线图' },
      { value: 'pie', label: '饼图' },
      { value: 'area', label: '面积图' },
      { value: 'scatter', label: '散点图' }
    ];

    // 图例位置选项
    const legendPositions = [
      { value: 'top', label: '顶部' },
      { value: 'bottom', label: '底部' },
      { value: 'left', label: '左侧' },
      { value: 'right', label: '右侧' }
    ];

    // 图例纵向位置选项
    const legendVerticalPositions = [
      { value: 'top', label: '顶部' },
      { value: 'middle', label: '居中' },
      { value: 'bottom', label: '底部' }
    ];

    // 监听 props 变化
    watch(() => props.chartData, (newData) => {
      if (newData) {
        Object.assign(formData, JSON.parse(JSON.stringify(newData)));
        updatePreview();
      }
    }, { immediate: true, deep: true });

    // 监听表单数据变化，实时更新预览
    watch(formData, () => {
      updatePreview();
    }, { deep: true });

    // 监听弹窗显示状态
    watch(() => props.visible, (visible) => {
      if (visible) {
        nextTick(() => {
          updatePreview();
        });
      } else {
        // 清理图表实例
        if (chartInstance.value) {
          chartInstance.value.dispose();
          chartInstance.value = null;
        }
      }
    });

    // 更新预览图表
    const updatePreview = async () => {
      if (!props.visible || !previewContainer.value) return;

      try {
        // 获取或加载 ECharts
        let echarts;
        if (window.echarts) {
          echarts = window.echarts;
        } else {
          const echartsModule = await import('echarts');
          echarts = echartsModule.default || echartsModule;
          window.echarts = echarts;
        }

        // 销毁旧的图表实例
        if (chartInstance.value) {
          chartInstance.value.dispose();
          chartInstance.value = null;
        }

        // 创建新的图表实例
        chartInstance.value = echarts.init(previewContainer.value);

        // 生成图表配置
        const option = generateEChartsOption(formData);

        // 设置图表配置
        chartInstance.value.setOption(option, true);

        // 监听窗口大小变化
        const resizeHandler = () => {
          if (chartInstance.value) {
            chartInstance.value.resize();
          }
        };
        window.addEventListener('resize', resizeHandler);

      } catch (error) {
        console.error('Preview update failed:', error);
      }
    };

    // 生成 ECharts 配置
    const generateEChartsOption = (chartData) => {
      const { type, title, data, config } = chartData;

      const baseOption = {
        title: {
          text: title || '图表标题',
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: config.showTooltip !== false ? {
          trigger: type === 'pie' ? 'item' : 'axis',
          axisPointer: {
            type: type === 'line' || type === 'area' ? 'cross' : 'shadow'
          }
        } : undefined,
        legend: config.showLegend !== false ? {
          orient: config.legendPosition === 'left' || config.legendPosition === 'right' ? 'vertical' : 'horizontal',
          left: config.legendPosition === 'left' ? 0 :
                config.legendPosition === 'right' ? 'right' : 'center',
          right: config.legendPosition === 'right' ? 0 : undefined,
          bottom: config.legendPosition === 'bottom' ? 0 : undefined,
          top: config.legendPosition === 'top' ? 40 : undefined
        } : undefined,
        grid: config.showGrid !== false && type !== 'pie' ? {
          left: config.legendPosition === 'left' ? '15%' : '3%',
          right: config.legendPosition === 'right' ? '15%' : '4%',
          bottom: config.legendPosition === 'bottom' ? '15%' : '3%',
          top: title ? 80 : 40,
          containLabel: true
        } : undefined,
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
      };

      // 根据图表类型生成不同的配置
      switch (type) {
        case 'bar':
          return {
            ...baseOption,
            xAxis: {
              type: 'category',
              data: data.categories || [],
              axisLabel: {
                interval: 0,
                rotate: (data.categories || []).some(cat => cat.length > 4) ? 45 : 0
              }
            },
            yAxis: {
              type: 'value',
              splitLine: {
                show: config.showGrid !== false
              }
            },
            series: (data.series || []).map(s => ({
              name: s.name,
              type: 'bar',
              data: s.data || [],
              emphasis: {
                focus: 'series'
              }
            }))
          };

        case 'line':
          return {
            ...baseOption,
            xAxis: {
              type: 'category',
              data: data.categories || [],
              boundaryGap: false
            },
            yAxis: {
              type: 'value',
              splitLine: {
                show: config.showGrid !== false
              }
            },
            series: (data.series || []).map(s => ({
              name: s.name,
              type: 'line',
              data: s.data || [],
              smooth: true,
              emphasis: {
                focus: 'series'
              }
            }))
          };

        case 'area':
          return {
            ...baseOption,
            xAxis: {
              type: 'category',
              data: data.categories || [],
              boundaryGap: false
            },
            yAxis: {
              type: 'value',
              splitLine: {
                show: config.showGrid !== false
              }
            },
            series: (data.series || []).map(s => ({
              name: s.name,
              type: 'line',
              data: s.data || [],
              smooth: true,
              areaStyle: {},
              emphasis: {
                focus: 'series'
              }
            }))
          };

        case 'pie':
          return {
            ...baseOption,
            grid: undefined,
            series: [{
              name: (data.series && data.series[0]) ? data.series[0].name : '数据',
              type: 'pie',
              radius: ['0%', '70%'],
              center: ['50%', '60%'],
              data: (data.categories || []).map((cat, index) => ({
                name: cat,
                value: (data.series && data.series[0] && data.series[0].data) ? data.series[0].data[index] || 0 : 0
              })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }]
          };

        case 'scatter':
          return {
            ...baseOption,
            xAxis: {
              type: 'value',
              splitLine: {
                show: config.showGrid !== false
              }
            },
            yAxis: {
              type: 'value',
              splitLine: {
                show: config.showGrid !== false
              }
            },
            series: (data.series || []).map(s => ({
              name: s.name,
              type: 'scatter',
              data: (s.data || []).map((value, index) => [index, value]),
              symbolSize: 8,
              emphasis: {
                focus: 'series'
              }
            }))
          };

        default:
          return baseOption;
      }
    };

    // 添加类别
    const addCategory = () => {
      formData.data.categories.push(`类别${formData.data.categories.length + 1}`);
      // 为每个系列添加对应的数据点
      formData.data.series.forEach(series => {
        series.data.push(0);
      });
    };

    // 删除类别
    const removeCategory = (index) => {
      formData.data.categories.splice(index, 1);
      // 删除每个系列对应的数据点
      formData.data.series.forEach(series => {
        series.data.splice(index, 1);
      });
    };

    // 添加系列
    const addSeries = () => {
      const newSeries = {
        name: `系列${formData.data.series.length + 1}`,
        data: new Array(formData.data.categories.length).fill(0)
      };
      formData.data.series.push(newSeries);
    };

    // 删除系列
    const removeSeries = (index) => {
      formData.data.series.splice(index, 1);
    };

    // 处理确认
    const handleConfirm = () => {
      // 验证和清理数据
      const cleanData = {
        type: formData.type || 'bar',
        title: formData.title || '新建图表',
        data: {
          categories: (formData.data.categories || []).filter(cat => cat && cat.trim()),
          series: (formData.data.series || []).map(s => ({
            name: s.name || '系列',
            data: (s.data || []).map(d => parseFloat(d) || 0)
          })).filter(s => s.name.trim())
        },
        config: {
          theme: formData.config.theme || 'default',
          showLegend: formData.config.showLegend !== false,
          legendPosition: formData.config.legendPosition || 'bottom',
          legendVerticalPosition: formData.config.legendVerticalPosition || 'middle',
          showGrid: formData.config.showGrid !== false,
          showTooltip: formData.config.showTooltip !== false
        }
      };

      // 确保数据系列的长度与类别一致
      cleanData.data.series.forEach(series => {
        while (series.data.length < cleanData.data.categories.length) {
          series.data.push(0);
        }
        series.data = series.data.slice(0, cleanData.data.categories.length);
      });

      console.log('Confirming chart data:', cleanData);
      emit('confirm', cleanData);
      emit('update:visible', false);
    };

    // 处理取消
    const handleCancel = () => {
      emit('cancel');
      emit('update:visible', false);
    };

    // 渲染设置面板
    const renderSettingsPanel = () => {
      return h('div', { class: `${prefixClass}-chart-editor__settings` }, [
        // 图表类型选择
        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__label` }, '图表类型'),
          h('div', { class: `${prefixClass}-chart-editor__chart-types` }, 
            chartTypes.map(type => 
              h('div', {
                class: [
                  `${prefixClass}-chart-editor__chart-type`,
                  { active: formData.type === type.value }
                ],
                onClick: () => formData.type = type.value
              }, [
                h(IIcon, { name: type.icon, size: 20 }),
                h('span', type.label)
              ])
            )
          )
        ]),

        // 图表标题
        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__label` }, '标题名称'),
          h('input', {
            type: 'text',
            class: `${prefixClass}-chart-editor__input`,
            value: formData.title,
            onInput: (e) => formData.title = e.target.value,
            placeholder: '请输入标题名称...'
          })
        ]),

        // 显示图例
        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__checkbox-label` }, [
            h('input', {
              type: 'checkbox',
              checked: formData.config.showLegend,
              onChange: (e) => formData.config.showLegend = e.target.checked
            }),
            h('span', '显示图例')
          ])
        ]),

        // 图例布局
        formData.config.showLegend && h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__label` }, '图例布局'),
          h('select', {
            class: `${prefixClass}-chart-editor__select`,
            value: formData.config.legendPosition,
            onChange: (e) => formData.config.legendPosition = e.target.value
          }, legendPositions.map(pos => 
            h('option', { value: pos.value }, pos.label)
          ))
        ]),

        // 图例纵向位置
        formData.config.showLegend && h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__label` }, '图例纵向位置'),
          h('select', {
            class: `${prefixClass}-chart-editor__select`,
            value: formData.config.legendVerticalPosition || 'center',
            onChange: (e) => formData.config.legendVerticalPosition = e.target.value
          }, [
            h('option', { value: 'top' }, '顶部'),
            h('option', { value: 'center' }, '居中'),
            h('option', { value: 'bottom' }, '底部')
          ])
        ]),

        // 其他配置选项
        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__checkbox-label` }, [
            h('input', {
              type: 'checkbox',
              checked: formData.config.showGrid,
              onChange: (e) => formData.config.showGrid = e.target.checked
            }),
            h('span', '显示网格')
          ])
        ]),

        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__checkbox-label` }, [
            h('input', {
              type: 'checkbox',
              checked: formData.config.showTooltip,
              onChange: (e) => formData.config.showTooltip = e.target.checked
            }),
            h('span', '显示提示框')
          ])
        ])
      ]);
    };

    // 渲染数据面板
    const renderDataPanel = () => {
      return h('div', { class: `${prefixClass}-chart-editor__data` }, [
        // 模式切换
        h('div', { class: `${prefixClass}-chart-editor__mode-switch` }, [
          h('button', {
            class: [
              `${prefixClass}-chart-editor__mode-btn`,
              { active: editMode.value === 'basic' }
            ],
            onClick: () => editMode.value = 'basic'
          }, '基础模式'),
          h('button', {
            class: [
              `${prefixClass}-chart-editor__mode-btn`,
              { active: editMode.value === 'advanced' }
            ],
            onClick: () => editMode.value = 'advanced'
          }, '高级模式')
        ]),

        editMode.value === 'basic' ? renderBasicDataEditor() : renderAdvancedDataEditor()
      ]);
    };

    // 渲染基础数据编辑器
    const renderBasicDataEditor = () => {
      return h('div', { class: `${prefixClass}-chart-editor__basic-editor` }, [
        // 类别管理
        h('div', { class: `${prefixClass}-chart-editor__section` }, [
          h('div', { class: `${prefixClass}-chart-editor__section-header` }, [
            h('h4', '类别'),
            h(IButton, {
              size: 'small',
              onClick: addCategory
            }, {
              icon: () => h(IIcon, { name: 'plus', size: 12 }),
              default: () => '添加'
            })
          ]),
          h('div', { class: `${prefixClass}-chart-editor__categories` },
            formData.data.categories.map((category, index) =>
              h('div', { class: `${prefixClass}-chart-editor__category-item` }, [
                h('input', {
                  type: 'text',
                  class: `${prefixClass}-chart-editor__input`,
                  value: category,
                  onInput: (e) => formData.data.categories[index] = e.target.value
                }),
                h(IButton, {
                  size: 'small',
                  type: 'danger',
                  onClick: () => removeCategory(index)
                }, {
                  icon: () => h(IIcon, { name: 'trash', size: 12 })
                })
              ])
            )
          )
        ]),

        // 系列管理
        h('div', { class: `${prefixClass}-chart-editor__section` }, [
          h('div', { class: `${prefixClass}-chart-editor__section-header` }, [
            h('h4', '数据系列'),
            h(IButton, {
              size: 'small',
              onClick: addSeries
            }, {
              icon: () => h(IIcon, { name: 'plus', size: 12 }),
              default: () => '添加'
            })
          ]),
          h('div', { class: `${prefixClass}-chart-editor__series` },
            formData.data.series.map((series, seriesIndex) =>
              h('div', { class: `${prefixClass}-chart-editor__series-item` }, [
                h('div', { class: `${prefixClass}-chart-editor__series-header` }, [
                  h('input', {
                    type: 'text',
                    class: `${prefixClass}-chart-editor__input`,
                    value: series.name,
                    onInput: (e) => formData.data.series[seriesIndex].name = e.target.value,
                    placeholder: '系列名称'
                  }),
                  h(IButton, {
                    size: 'small',
                    type: 'danger',
                    onClick: () => removeSeries(seriesIndex)
                  }, {
                    icon: () => h(IIcon, { name: 'trash', size: 12 })
                  })
                ]),
                h('div', { class: `${prefixClass}-chart-editor__series-data` },
                  series.data.map((value, dataIndex) =>
                    h('input', {
                      type: 'number',
                      class: `${prefixClass}-chart-editor__input`,
                      value: value,
                      onInput: (e) => formData.data.series[seriesIndex].data[dataIndex] = parseFloat(e.target.value) || 0,
                      placeholder: formData.data.categories[dataIndex] || `数据${dataIndex + 1}`
                    })
                  )
                )
              ])
            )
          )
        ])
      ]);
    };

    // 渲染高级数据编辑器
    const renderAdvancedDataEditor = () => {
      const jsonString = JSON.stringify(formData.data, null, 2);
      
      return h('div', { class: `${prefixClass}-chart-editor__advanced-editor` }, [
        h('div', { class: `${prefixClass}-chart-editor__json-hint` }, 
          'ECharts 格式数据编辑（请确保 JSON 格式正确）'
        ),
        h('textarea', {
          class: `${prefixClass}-chart-editor__json-editor`,
          value: jsonString,
          onInput: (e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              formData.data = parsed;
            } catch (error) {
              // JSON 格式错误时不更新数据
              console.warn('Invalid JSON format');
            }
          },
          placeholder: '请输入符合 ECharts 格式的 JSON 数据...'
        })
      ]);
    };

    // 渲染预览面板
    const renderPreviewPanel = () => {
      return h('div', { class: `${prefixClass}-chart-editor__preview` }, [
        h('div', {
          class: `${prefixClass}-chart-editor__preview-container`,
          style: {
            width: '100%',
            height: '400px',
            border: '1px solid #e8e8e8',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: '#fafafa',
            color: '#666',
            fontSize: '14px'
          }
        }, [
          h('div', { style: { textAlign: 'center' } }, [
            h('div', { style: { fontSize: '24px', marginBottom: '8px' } }, '📊'),
            h('div', `${formData.title || '图表预览'}`),
            h('div', { style: { fontSize: '12px', color: '#999', marginTop: '4px' } },
              `${formData.type === 'bar' ? '柱状图' :
                formData.type === 'line' ? '折线图' :
                formData.type === 'pie' ? '饼图' : '图表'} 预览`)
          ])
        ])
      ]);
    };

    // 渲染Excel式数据表格
    const renderDataTable = () => {
      const categories = formData.data.categories || [];
      const series = formData.data.series || [];

      return h('div', { class: `${prefixClass}-chart-editor__data-table` }, [
        // 表格容器
        h('div', { class: `${prefixClass}-chart-editor__table-container` }, [
          h('table', { class: `${prefixClass}-chart-editor__table` }, [
            // 表头
            h('thead', [
              h('tr', [
                h('th', { class: `${prefixClass}-chart-editor__table-header` }, '类别'),
                ...series.map((s, index) =>
                  h('th', { class: `${prefixClass}-chart-editor__table-header` }, [
                    h('input', {
                      type: 'text',
                      class: `${prefixClass}-chart-editor__table-input`,
                      value: s.name,
                      onInput: (e) => formData.data.series[index].name = e.target.value,
                      placeholder: `系列${index + 1}`
                    }),
                    h('button', {
                      class: `${prefixClass}-chart-editor__table-remove-btn`,
                      onClick: () => removeSeries(index),
                      title: '删除系列'
                    }, '×')
                  ])
                ),
                h('th', { class: `${prefixClass}-chart-editor__table-header` }, [
                  h('button', {
                    class: `${prefixClass}-chart-editor__table-add-btn`,
                    onClick: addSeries,
                    title: '添加系列'
                  }, '+')
                ])
              ])
            ]),

            // 表体
            h('tbody', [
              ...categories.map((cat, rowIndex) =>
                h('tr', { key: rowIndex }, [
                  // 类别列
                  h('td', { class: `${prefixClass}-chart-editor__table-cell` }, [
                    h('input', {
                      type: 'text',
                      class: `${prefixClass}-chart-editor__table-input`,
                      value: cat,
                      onInput: (e) => formData.data.categories[rowIndex] = e.target.value,
                      placeholder: `类别${rowIndex + 1}`
                    }),
                    h('button', {
                      class: `${prefixClass}-chart-editor__table-remove-btn`,
                      onClick: () => removeCategory(rowIndex),
                      title: '删除类别'
                    }, '×')
                  ]),

                  // 数据列
                  ...series.map((s, colIndex) =>
                    h('td', { class: `${prefixClass}-chart-editor__table-cell` }, [
                      h('input', {
                        type: 'number',
                        class: `${prefixClass}-chart-editor__table-input`,
                        value: s.data[rowIndex] || 0,
                        onInput: (e) => {
                          const value = parseFloat(e.target.value) || 0;
                          if (!formData.data.series[colIndex].data) {
                            formData.data.series[colIndex].data = [];
                          }
                          formData.data.series[colIndex].data[rowIndex] = value;
                        },
                        placeholder: '0'
                      })
                    ])
                  ),

                  // 空白列
                  h('td', { class: `${prefixClass}-chart-editor__table-cell` })
                ])
              ),

              // 添加行按钮
              h('tr', [
                h('td', { class: `${prefixClass}-chart-editor__table-cell` }, [
                  h('button', {
                    class: `${prefixClass}-chart-editor__table-add-btn`,
                    onClick: addCategory,
                    title: '添加类别'
                  }, '+ 添加类别')
                ]),
                ...series.map(() => h('td', { class: `${prefixClass}-chart-editor__table-cell` })),
                h('td', { class: `${prefixClass}-chart-editor__table-cell` })
              ])
            ])
          ])
        ])
      ]);
    };

    // 渲染基础配置
    const renderBasicConfig = () => {
      return h('div', { class: `${prefixClass}-chart-editor__basic-config` }, [
        // 图表类型
        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__label` }, '图表类型'),
          h('select', {
            class: `${prefixClass}-chart-editor__select`,
            value: formData.type,
            onChange: (e) => formData.type = e.target.value
          }, chartTypes.map(type =>
            h('option', { value: type.value }, type.label)
          ))
        ]),

        // 标题名称
        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__label` }, '标题名称'),
          h('input', {
            type: 'text',
            class: `${prefixClass}-chart-editor__input`,
            value: formData.title,
            onInput: (e) => formData.title = e.target.value,
            placeholder: '请输入标题名称...'
          })
        ]),

        // 显示图例
        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__checkbox-label` }, [
            h('input', {
              type: 'checkbox',
              checked: formData.config.showLegend,
              onChange: (e) => formData.config.showLegend = e.target.checked
            }),
            h('span', '显示图例')
          ])
        ]),

        // 图例位置
        formData.config.showLegend ? h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__label` }, '图例位置'),
          h('select', {
            class: `${prefixClass}-chart-editor__select`,
            value: formData.config.legendPosition,
            onChange: (e) => formData.config.legendPosition = e.target.value
          }, legendPositions.map(pos =>
            h('option', { value: pos.value }, pos.label)
          ))
        ]) : null,

        // 图例纵向位置
        formData.config.showLegend && (formData.config.legendPosition === 'left' || formData.config.legendPosition === 'right') ?
        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__label` }, '图例纵向位置'),
          h('select', {
            class: `${prefixClass}-chart-editor__select`,
            value: formData.config.legendVerticalPosition || 'middle',
            onChange: (e) => formData.config.legendVerticalPosition = e.target.value
          }, legendVerticalPositions.map(pos =>
            h('option', { value: pos.value }, pos.label)
          ))
        ]) : null,

        // Excel式数据表格
        h('div', { class: `${prefixClass}-chart-editor__form-group` }, [
          h('label', { class: `${prefixClass}-chart-editor__label` }, '数据配置'),
          renderDataTable()
        ])
      ]);
    };

    // 渲染高级配置
    const renderAdvancedConfig = () => {
      return h('div', { class: `${prefixClass}-chart-editor__advanced-config` }, [
        h('div', { class: `${prefixClass}-chart-editor__json-hint` },
          '在此处输入符合 ECharts 格式的 JSON 数据配置'
        ),
        h('textarea', {
          class: `${prefixClass}-chart-editor__json-editor`,
          value: JSON.stringify(formData, null, 2),
          onInput: (e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              Object.assign(formData, parsed);
            } catch (error) {
              // JSON 格式错误时不更新数据
              console.warn('Invalid JSON format');
            }
          },
          placeholder: '请输入符合 ECharts 格式的 JSON 数据...'
        })
      ]);
    };

    return () => {
      if (!props.visible) return null;

      return h('div', { class: `${prefixClass}-chart-editor__overlay` }, [
        h('div', { class: `${prefixClass}-chart-editor__modal` }, [
          // 头部
          h('div', { class: `${prefixClass}-chart-editor__header` }, [
            h('div', { class: `${prefixClass}-chart-editor__title` }, [
              h(IIcon, { name: 'chart', size: 20 }),
              h('h3', '新建图表')
            ]),
            h('button', {
              class: `${prefixClass}-chart-editor__close`,
              onClick: handleCancel
            }, '×')
          ]),

          // 主体内容 - 左右分栏
          h('div', { class: `${prefixClass}-chart-editor__body` }, [
            // 左侧预览区域
            h('div', { class: `${prefixClass}-chart-editor__preview-section` }, [
              h('div', { class: `${prefixClass}-chart-editor__preview-header` }, [
                h('span', '预览')
              ]),
              h('div', {
                ref: previewContainer,
                class: `${prefixClass}-chart-editor__preview-container`,
                style: {
                  width: '100%',
                  height: '400px',
                  background: '#fff',
                  border: '1px solid #e8e8e8',
                  borderRadius: '4px'
                }
              })
            ]),

            // 右侧配置区域
            h('div', { class: `${prefixClass}-chart-editor__config-section` }, [
              // 模式切换
              h('div', { class: `${prefixClass}-chart-editor__mode-tabs` }, [
                h('button', {
                  class: [
                    `${prefixClass}-chart-editor__mode-tab`,
                    { active: editMode.value === 'basic' }
                  ],
                  onClick: () => editMode.value = 'basic'
                }, '基础模式'),
                h('button', {
                  class: [
                    `${prefixClass}-chart-editor__mode-tab`,
                    { active: editMode.value === 'advanced' }
                  ],
                  onClick: () => editMode.value = 'advanced'
                }, '高级模式')
              ]),

              // 配置内容
              h('div', { class: `${prefixClass}-chart-editor__config-content` }, [
                editMode.value === 'basic' ? renderBasicConfig() : renderAdvancedConfig()
              ])
            ])
          ]),

          // 底部按钮
          h('div', { class: `${prefixClass}-chart-editor__footer` }, [
            h(IButton, {
              onClick: handleCancel
            }, '取消'),
            h(IButton, {
              type: 'primary',
              onClick: handleConfirm
            }, '确认')
          ])
        ])
      ]);
    };
  }
});