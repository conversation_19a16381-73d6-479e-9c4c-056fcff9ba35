# 版本恢复功能测试指南

## 测试步骤

### 1. 创建测试内容
1. 打开编辑器
2. 输入一些测试内容，比如：
   - 标题：测试文档
   - 段落：这是一个测试段落
   - 列表：测试列表项1、测试列表项2

### 2. 保存版本
1. 点击工具栏的"版本"按钮
2. 点击"保存当前版本"
3. 输入版本标题：初始版本
4. 确认保存

### 3. 修改内容
1. 删除或修改现有内容
2. 添加新的内容
3. 确保内容有明显变化

### 4. 再次保存版本
1. 再次点击"保存当前版本"
2. 输入版本标题：修改版本
3. 确认保存

### 5. 测试版本恢复
1. 在版本管理面板中找到"初始版本"
2. 点击"恢复"按钮
3. 确认恢复操作
4. 检查编辑器内容是否恢复到初始状态

## 预期结果

- ✅ 版本保存成功
- ✅ 版本列表显示正确
- ✅ 版本恢复后编辑器内容正确更新
- ✅ 编辑器状态正常，可以继续编辑

## 问题排查

如果版本恢复不生效，请检查：

1. 浏览器控制台是否有错误信息
2. 网络请求是否成功
3. 版本内容格式是否正确
4. 编辑器是否正确初始化

## 修复内容

### 主要修复点：

1. **版本恢复逻辑优化**：
   - 使用 `editor.commands.setContent()` 替代直接设置 `content.value`
   - 添加多种内容格式的处理逻辑
   - 强制更新编辑器状态

2. **内容传递修复**：
   - 使用计算属性 `currentEditorContent` 获取最新编辑器内容
   - 确保版本管理组件获取到正确的当前内容

3. **错误处理增强**：
   - 添加详细的错误日志
   - 提供用户友好的错误提示

### 技术细节：

```javascript
// 修复前的版本恢复（不生效）
const handleRestoreVersion = (versionContent) => {
  content.value = versionContent  // 直接设置响应式变量，不会触发编辑器更新
  Message.success('版本恢复成功')
}

// 修复后的版本恢复（生效）
const handleRestoreVersion = (versionContent) => {
  if (editorEl.value?.editor && versionContent) {
    try {
      // 使用TipTap命令设置内容
      editorEl.value.editor.commands.setContent(versionContent, false)
      // 强制更新编辑器状态
      editorEl.value.editor.commands.focus()
      // 更新响应式变量
      content.value = editorEl.value.editor.getHTML()
      Message.success('版本恢复成功')
    } catch (error) {
      console.error('版本恢复失败:', error)
      Message.error('版本恢复失败: ' + error.message)
    }
  }
}
```

## 测试验证

请按照上述步骤测试版本恢复功能，确认修复是否生效。
