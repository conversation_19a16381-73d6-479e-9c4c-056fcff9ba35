# 版本管理与持久化分析与方案

## 现状评估

### 功能覆盖
- 手动/快捷/自动保存版本、版本列表/分页、编辑/删除：已实现；自动保存版本禁止删除逻辑正确。
- 版本预览：未完成（原实现仅 JSON 文本，不是 HTML）。
- 版本对比：标注“开发中”，未实现。
- 版本恢复：在协同编辑（Yjs + TipTap Collaboration）场景下不可靠，直接设置响应式数据或 setContent 易被协同状态覆盖。

### 持久化现状
- 文档列表：仅内存，服务重启丢失。
- 版本记录：仅内存，服务重启丢失。
- 协同文档内容（Y.Doc）：仓库内存在基于文件的持久化实现（`server/src/index_v1.js` + `server/src/lib/ydoc.js` 写入 `server/doc-storage/*.ydoc`），但当前运行入口使用 `server/src/index.js`，未启用该持久化路径。

## 已选方案与改动

### 任务1：新建文档持久化（已完成）
- 方案：为文档列表增加“文件持久化”，存放于 `server/src/db/document/documents.json`。
- 关键文件：`server/src/router/document.js`
  - 新增读写函数：`loadDocuments()`、`saveDocuments()`，不存在则写入默认数据。
  - `GET/POST/PUT/DELETE /documents` 全部改为读写文件而非内存变量。
- 影响：服务重启后，文档列表仍然存在；与现有路由与前端 API 完全兼容。

### 任务2：版本预览 HTML 渲染（已完成）
- 方案：在前端创建一个只读的临时 TipTap Editor，将版本 JSON 内容渲染为 HTML 再展示。
- 关键文件：`app/src/components/VersionManager.vue`
  - `handlePreviewVersion()` 内动态导入常用 TipTap 扩展，构建只读 Editor，`getHTML()` 结果赋值到弹窗。
- 影响：版本预览从 JSON 文本→真实 HTML 渲染，体验明显提升。

## 待定方案与建议（后续）

### A. 版本记录与文档列表持久化（任务3）
两种可选：
1) 文件持久化（无数据库）
   - 版本记录：每个文档一个 JSON 文件，如 `server/src/db/version/<docId>.json`，追加写入版本条目；`GET/POST/DELETE/PUT` 路由对应读写该文件。
   - 文档列表：已完成文件持久化（见任务1）。
   - 优点：落地快、依赖少；缺点：并发与大体量数据下的性能与一致性需要额外保护。
2) 数据库持久化（推荐中长期）
   - 选用 SQLite/PostgreSQL/MySQL 皆可；版本记录与文档列表表结构清晰。
   - 优点：并发、查询、统计能力更强；缺点：引入运维成本。

当前建议：短期先走“文件持久化”，快速交付；后续按需要切换数据库。

### B. 协同文档内容持久化
两种可选：
1) 切换服务入口到 `server/src/index_v1.js`（已具备将 Y.Doc 持久化为 `.ydoc` 文件的能力）
2) 将 `index_v1.js` 的 `lib/ydoc.js` 引入 `index.js`，统一入口

建议：短期直接使用 `index_v1.js` 作为入口，最小改动启用 Y.Doc 落盘；后续再合并入口逻辑。

### C. 版本恢复在协同模式下的正确写法
- 客户端：优先通过 Y.Doc（绑定的 `Y.XmlFragment`）进行事务更新，而非仅 `setContent` 或修改响应式 `content`；确保协同状态不可逆地应用目标版本内容。
- 服务器端（可选）：提供“恢复版本”接口，直接替换服务器 Y.Doc 状态并广播（适合强一致场景）。

### D. 版本对比
- 短期：后端返回两份版本 JSON，前端做文本/DOM diff 高亮。
- 中期：基于 ProseMirror Node 的结构化 diff，差异定位更精准。

## 开发任务清单

- [x] 任务1：新建文档持久化（文件持久化）
  - 路由读写：`server/src/router/document.js`
  - 存储位置：`server/src/db/document/documents.json`
  - 状态：已完成

- [x] 任务2：版本预览 HTML 渲染
  - 改造位置：`app/src/components/VersionManager.vue` → `handlePreviewVersion()`
  - 方式：临时只读 Editor 渲染 JSON → HTML
  - 状态：已完成

- [ ] 任务3：版本记录与文档列表持久化（版本记录部分）
  - 路由文件：`server/src/router/version.js`
  - 存储建议：`server/src/db/version/<docId>.json`
  - 事项：落盘/加载/删除/编辑，含并发文件锁与异常保护

- [ ] 任务4：启用协同内容（Y.Doc）持久化
  - 方案一：使用 `server/src/index_v1.js` 作为运行入口
  - 方案二：合并 `lib/ydoc.js` 到 `index.js`

- [ ] 任务5：版本恢复在协同模式下的改造
  - 客户端通过 Y.Doc 事务应用 JSON（或后端恢复接口）

- [ ] 任务6：版本对比功能
  - 短期文本 diff，中期结构化 diff

## 里程碑与回归
- 完成任务1/2 后，重启服务不再影响“文档列表”；版本预览体验改善。
- 完成任务3 后，版本记录在服务重启后仍可用。
- 完成任务4/5 后，协同场景下的“版本恢复”可稳定落地。


