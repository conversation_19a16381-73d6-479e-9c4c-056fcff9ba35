import { defineComponent, h, ref } from "vue";
import { prefixClass, t } from "../../../core";
import { IButton, ITooltip, IIcon } from "../ui";
import ChartEditor from "../chart/chart-editor.js";

export default defineComponent({
  name: "ButtonChart",
  props: {
    editor: {
      type: Object,
      required: true,
    },
    menu: {
      type: Object,
      required: true,
    }
  },
  setup(props) {
    const showEditor = ref(false);
    const currentChartData = ref(null);

    // 打开图表编辑器
    const openChartEditor = (chartData = null) => {
      currentChartData.value = chartData || {
        type: "bar",
        title: "新建图表",
        data: {
          categories: ["类别1", "类别2", "类别3", "类别4"],
          series: [
            {
              name: "系列1",
              data: [4.3, 2.5, 4.5, 5.0]
            }
          ]
        },
        config: {
          theme: "default",
          showLegend: true,
          legendPosition: "bottom",
          showGrid: true,
          showTooltip: true
        }
      };
      showEditor.value = true;
    };

    // 确认图表
    const handleConfirm = (chartData) => {
      console.log('Chart button handleConfirm called with:', chartData);
      console.log('Chart data JSON:', JSON.stringify(chartData));

      try {
        // 检查是否是编辑现有图表
        if (currentChartData.value && window.currentChartPosition !== undefined) {
          console.log('Updating existing chart at position:', window.currentChartPosition);
          // 更新现有图表
          const { editor } = props;

          // 使用updateAttributes方法更新图表数据
          const result = editor
            .chain()
            .focus()
            .updateAttributes('chart', {
              data: JSON.stringify(chartData)
            })
            .run();

          console.log('Update result:', result);

          // 如果updateAttributes失败，尝试setNodeMarkup
          if (!result) {
            console.log('Fallback to setNodeMarkup');
            const fallbackResult = editor
              .chain()
              .focus()
              .setNodeMarkup(window.currentChartPosition, undefined, {
                data: JSON.stringify(chartData)
              })
              .run();
            console.log('Fallback result:', fallbackResult);
          }

          window.currentChartPosition = undefined;
        } else if (window.isSlashCommand && window.slashEditor) {
          console.log('Inserting chart from slash command');
          // 从斜杠命令触发的插入
          let result = window.slashEditor
            .chain()
            .focus()
            .setChart({
              data: JSON.stringify(chartData)
            })
            .run();

          console.log('Slash command insert result:', result);

          // 如果setChart失败，尝试insertContent
          if (!result) {
            console.log('Fallback to insertContent for slash command');
            result = window.slashEditor
              .chain()
              .focus()
              .insertContent({
                type: "chart",
                attrs: {
                  data: JSON.stringify(chartData)
                }
              })
              .run();
            console.log('Slash command fallback result:', result);
          }

          // 清理标记
          window.isSlashCommand = false;
          window.slashEditor = null;
        } else {
          console.log('Inserting new chart from toolbar');
          // 从工具栏触发的插入
          const { editor } = props;

          // 首先尝试使用专门的setChart命令
          let result = editor
            .chain()
            .focus()
            .setChart({
              data: JSON.stringify(chartData)
            })
            .run();

          console.log('setChart result:', result);

          // 如果失败，尝试通用方法
          if (!result) {
            console.log('Fallback to insertContent');
            result = editor
              .chain()
              .focus()
              .insertContent({
                type: "chart",
                attrs: {
                  data: JSON.stringify(chartData)
                }
              })
              .run();
            console.log('Fallback result:', result);
          }

          // 强制触发一次编辑器更新
          if (result) {
            setTimeout(() => {
              editor.commands.focus();
            }, 100);
          }

          console.log('Editor content after insert:', editor.getHTML());

          // 检查是否真的插入了
          const content = editor.getJSON();
          console.log('Editor JSON content:', content);
        }

        showEditor.value = false;
        console.log('Chart editor closed');
      } catch (error) {
        console.error('Error in handleConfirm:', error);
      }
    };

    // 取消编辑
    const handleCancel = () => {
      showEditor.value = false;
    };

    // 注册全局方法供图表组件调用
    window.openChartEditor = openChartEditor;

    return () => [
      h(
        ITooltip,
        { 
          text: t(props.menu.name), 
          shortcutkeys: props.menu.shortcutkeys 
        },
        {
          default: () =>
            h(
              IButton,
              {
                active: props.menu?.isActive && props.menu?.isActive({ editor: props.editor }),
                disabled: props.menu?.isDisabled && props.menu?.isDisabled({ editor: props.editor }),
                onClick: () => openChartEditor()
              },
              {
                icon: () =>
                  h(IIcon, {
                    name: props.menu.name,
                    size: 14,
                  }),
              },
            ),
        },
      ),

      // 图表编辑器弹窗
      h(ChartEditor, {
        visible: showEditor.value,
        chartData: currentChartData.value,
        "onUpdate:visible": (visible) => showEditor.value = visible,
        onConfirm: handleConfirm,
        onCancel: handleCancel
      })
    ];
  },
});