@use "config" as *;

.#{$prefix}.ProseMirror {
  // 图表容器样式
  .#{$prefix}__chart-container {
    position: relative;
    margin: 1rem 0;
    width: 100%;
    display: block;
    clear: both;

    &:hover .#{$prefix}__chart-edit-btn {
      opacity: 1;
    }
  }

  .#{$prefix}__chart-content {
    width: 100%;
    height: 400px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    position: relative;
    background: #fff;
    overflow: hidden;
    display: block;

    // 确保ECharts容器正确布局
    > div {
      width: 100% !important;
      height: 100% !important;
      display: block !important;
    }

    // 确保canvas元素正确布局
    canvas {
      width: 100% !important;
      height: 100% !important;
      display: block !important;
    }
  }

  .#{$prefix}__chart-edit-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 4px 8px;
    background: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.2s;

    &:hover {
      background: #40a9ff;
    }
  }
}

// 图表编辑器弹窗样式
.#{$prefix}-chart-editor__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.#{$prefix}-chart-editor__modal {
  width: 1200px;
  max-width: 95vw;
  max-height: 90vh;
  background: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.#{$prefix}-chart-editor__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;

  .#{$prefix}-chart-editor__title {
    display: flex;
    align-items: center;
    gap: 8px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.#{$prefix}-chart-editor__close {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
  color: #666;
  border-radius: 2px;

  &:hover {
    background: #f0f0f0;
  }
}

.#{$prefix}-chart-editor__tabs {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.#{$prefix}-chart-editor__tab {
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;

  &.active {
    color: #1890ff;
    border-bottom-color: #1890ff;
    background: white;
  }

  &:hover:not(.active) {
    color: #1890ff;
    background: #f0f9ff;
  }
}

// 主体内容 - 左右分栏
.#{$prefix}-chart-editor__body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧预览区域
.#{$prefix}-chart-editor__preview-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  border-right: 1px solid #e8e8e8;
  background: #fafafa;
}

.#{$prefix}-chart-editor__preview-header {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.#{$prefix}-chart-editor__preview-container {
  flex: 1;
  min-height: 400px;
}

// 右侧配置区域
.#{$prefix}-chart-editor__config-section {
  width: 400px;
  display: flex;
  flex-direction: column;
  background: white;
}

// 模式切换标签
.#{$prefix}-chart-editor__mode-tabs {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.#{$prefix}-chart-editor__mode-tab {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;

  &.active {
    color: #1890ff;
    border-bottom-color: #1890ff;
    background: white;
  }

  &:hover:not(.active) {
    color: #1890ff;
    background: #f0f9ff;
  }
}

// 配置内容
.#{$prefix}-chart-editor__config-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.#{$prefix}-chart-editor__footer {
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #fafafa;
}

// 基础配置样式
.#{$prefix}-chart-editor__basic-config {
  .#{$prefix}-chart-editor__form-group {
    margin-bottom: 20px;
  }

  .#{$prefix}-chart-editor__label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  .#{$prefix}-chart-editor__checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #333;

    input[type="checkbox"] {
      margin-right: 8px;
    }
  }

  .#{$prefix}-chart-editor__input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;

    &:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .#{$prefix}-chart-editor__select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    cursor: pointer;

    &:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  // 数据配置
  .#{$prefix}-chart-editor__data-config {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 16px;
    background: #fafafa;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }

  .#{$prefix}-chart-editor__categories,
  .#{$prefix}-chart-editor__series {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .#{$prefix}-chart-editor__category-item,
  .#{$prefix}-chart-editor__series-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;

    .#{$prefix}-chart-editor__input {
      flex: 1;
    }
  }

  .#{$prefix}-chart-editor__series-item {
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: white;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .#{$prefix}-chart-editor__series-data {
    margin-top: 8px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
  }

  .#{$prefix}-chart-editor__add-btn,
  .#{$prefix}-chart-editor__remove-btn {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }
  }

  .#{$prefix}-chart-editor__add-btn {
    background: #f0f9ff;
    border-color: #1890ff;
    color: #1890ff;

    &:hover {
      background: #1890ff;
      color: white;
    }
  }

  .#{$prefix}-chart-editor__remove-btn {
    color: #ff4d4f;
    border-color: #ffccc7;

    &:hover {
      background: #ff4d4f;
      color: white;
      border-color: #ff4d4f;
    }
  }

  // Excel式数据表格样式
  .#{$prefix}-chart-editor__data-table {
    margin-top: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;
    background: white;
  }

  .#{$prefix}-chart-editor__table-container {
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
  }

  .#{$prefix}-chart-editor__table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;

    th, td {
      border: 1px solid #e8e8e8;
      padding: 0;
      position: relative;
    }

    th {
      background: #fafafa;
      font-weight: 600;
      color: #333;
    }
  }

  .#{$prefix}-chart-editor__table-header {
    padding: 8px;
    text-align: center;
    min-width: 120px;
    position: relative;

    &:first-child {
      min-width: 100px;
    }

    &:last-child {
      min-width: 60px;
    }
  }

  .#{$prefix}-chart-editor__table-cell {
    padding: 4px;
    text-align: center;
    min-width: 120px;
    position: relative;

    &:first-child {
      min-width: 100px;
    }

    &:last-child {
      min-width: 60px;
    }
  }

  .#{$prefix}-chart-editor__table-input {
    width: 100%;
    border: none;
    padding: 6px 8px;
    font-size: 14px;
    text-align: center;
    background: transparent;

    &:focus {
      outline: 2px solid #1890ff;
      outline-offset: -2px;
      background: #f0f9ff;
    }

    &[type="number"] {
      text-align: right;
    }
  }

  .#{$prefix}-chart-editor__table-add-btn,
  .#{$prefix}-chart-editor__table-remove-btn {
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    padding: 4px 6px;
    border-radius: 2px;
    transition: all 0.2s;

    &:hover {
      background: #f0f0f0;
    }
  }

  .#{$prefix}-chart-editor__table-add-btn {
    color: #1890ff;
    font-weight: 600;

    &:hover {
      background: #f0f9ff;
      color: #0050b3;
    }
  }

  .#{$prefix}-chart-editor__table-remove-btn {
    color: #ff4d4f;
    font-weight: 600;
    position: absolute;
    top: 2px;
    right: 2px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    line-height: 1;

    &:hover {
      background: #fff2f0;
      color: #cf1322;
    }
  }
}

// 图表类型选择样式
.#{$prefix}-chart-editor__chart-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.#{$prefix}-chart-editor__chart-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;

  &:hover {
    border-color: #1890ff;
    background: #f0f9ff;
  }

  &.active {
    border-color: #1890ff;
    background: #e6f7ff;
    color: #1890ff;
  }

  span {
    margin-top: 8px;
    font-size: 12px;
    font-weight: 500;
  }
}

// 数据面板样式
.#{$prefix}-chart-editor__mode-switch {
  display: flex;
  margin-bottom: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}

.#{$prefix}-chart-editor__mode-btn {
  flex: 1;
  padding: 8px 16px;
  border: none;
  background: white;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-right: 1px solid #d9d9d9;
  transition: all 0.2s;

  &:last-child {
    border-right: none;
  }

  &.active {
    background: #1890ff;
    color: white;
  }

  &:hover:not(.active) {
    background: #f0f9ff;
    color: #1890ff;
  }
}

// 基础编辑器样式
.#{$prefix}-chart-editor__basic-editor {
  .#{$prefix}-chart-editor__section {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
  }

  .#{$prefix}-chart-editor__section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }

  .#{$prefix}-chart-editor__categories,
  .#{$prefix}-chart-editor__series {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .#{$prefix}-chart-editor__category-item,
  .#{$prefix}-chart-editor__series-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .#{$prefix}-chart-editor__input {
      flex: 1;
    }
  }

  .#{$prefix}-chart-editor__series-item {
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    background: #fafafa;
  }

  .#{$prefix}-chart-editor__series-data {
    margin-top: 8px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
  }
}

// 高级配置样式
.#{$prefix}-chart-editor__advanced-config {
  .#{$prefix}-chart-editor__json-hint {
    margin-bottom: 12px;
    padding: 8px 12px;
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    font-size: 12px;
    color: #586069;
  }

  .#{$prefix}-chart-editor__json-editor {
    width: 100%;
    height: 400px;
    padding: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
    background: #fafafa;

    &:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .#{$prefix}-chart-editor__modal {
    width: 95vw;
    height: 90vh;
  }

  .#{$prefix}-chart-editor__chart-types {
    grid-template-columns: repeat(2, 1fr);
  }

  .#{$prefix}-chart-editor__series-data {
    grid-template-columns: 1fr;
  }
}