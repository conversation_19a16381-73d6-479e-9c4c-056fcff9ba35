# 版本管理功能使用指南

## 功能概述

px-doc 协同文档编辑器现在支持完整的版本管理功能，帮助用户跟踪文档变化、防止数据丢失，并支持版本回滚操作。

## 主要功能

### 1. 自动版本保存
- **自动触发**：系统每5分钟自动保存一次文档状态
- **智能检测**：只有在内容发生变化时才会创建新版本
- **标识清晰**：自动保存的版本会标记为"自动保存"
- **保护机制**：自动保存的版本不能被删除

### 2. 手动版本管理
- **快捷保存**：使用 `Ctrl+S` (Windows) 或 `Cmd+S` (Mac) 快速保存版本
- **自定义信息**：可以为版本添加标题和描述
- **版本编辑**：可以修改已保存版本的标题和描述

### 3. 版本历史查看
- **完整列表**：查看文档的所有历史版本
- **详细信息**：显示版本创建时间、作者、文件大小等
- **分页加载**：支持大量版本的分页浏览

### 4. 版本操作
- **版本预览**：查看历史版本的内容
- **版本恢复**：回滚到指定的历史版本
- **版本删除**：删除不需要的版本（自动保存版本除外）
- **版本对比**：对比不同版本之间的差异（开发中）

## 使用方法

### 打开版本管理
1. **工具栏按钮**：点击编辑器工具栏中的"版本"按钮
2. **快捷键**：使用 `Ctrl+H` (Windows) 或 `Cmd+H` (Mac)

### 保存版本
1. **自动保存**：系统会自动保存，无需手动操作
2. **快速保存**：按 `Ctrl+S` 或 `Cmd+S` 快速保存当前状态
3. **手动保存**：
   - 打开版本管理面板
   - 点击"保存当前版本"按钮
   - 输入版本标题和描述
   - 确认保存

### 查看版本历史
1. 打开版本管理面板
2. 浏览版本列表，查看各版本信息
3. 点击"预览"按钮查看版本内容
4. 使用"加载更多"查看更多历史版本

### 恢复版本
1. 在版本列表中找到要恢复的版本
2. 点击版本操作菜单中的"恢复此版本"
3. 确认恢复操作
4. 系统会将编辑器内容恢复到选定版本

### 管理版本
1. **编辑版本信息**：
   - 点击版本操作菜单中的"编辑信息"
   - 修改标题和描述
   - 保存更改

2. **删除版本**：
   - 点击版本操作菜单中的"删除版本"
   - 确认删除操作
   - 注意：自动保存的版本不能删除

## 快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+S` / `Cmd+S` | 快速保存版本 |
| `Ctrl+H` / `Cmd+H` | 打开版本历史 |

## 版本信息说明

每个版本包含以下信息：
- **版本ID**：系统自动生成的唯一标识
- **标题**：版本的名称（可自定义）
- **描述**：版本的详细说明（可选）
- **创建时间**：版本创建的时间戳
- **作者**：创建版本的用户
- **文件大小**：版本内容的大小
- **类型标识**：区分自动保存和手动保存

## 最佳实践

### 版本命名建议
- 使用有意义的标题，如"添加用户认证功能"
- 在重要节点手动保存版本
- 为版本添加详细的描述说明

### 版本管理策略
- 定期清理不需要的手动保存版本
- 保留重要里程碑版本
- 利用自动保存作为安全备份

### 协同工作建议
- 团队成员在重要修改前保存版本
- 使用描述字段记录修改内容
- 定期同步和备份重要版本

## 注意事项

1. **数据安全**：版本数据目前存储在内存中，服务器重启会丢失
2. **存储限制**：大量版本可能影响性能，建议定期清理
3. **网络依赖**：版本操作需要网络连接
4. **浏览器兼容**：建议使用现代浏览器以获得最佳体验

## 故障排除

### 常见问题
1. **版本保存失败**：检查网络连接和服务器状态
2. **版本列表为空**：确认文档ID正确且有保存过版本
3. **恢复版本失败**：检查版本是否存在且有访问权限

### 联系支持
如遇到问题，请查看浏览器控制台的错误信息，或联系技术支持。

---

*版本管理功能持续改进中，欢迎反馈使用体验和建议！*